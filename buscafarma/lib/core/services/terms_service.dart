import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:buscafarma/core/services/legal_document_service.dart';
import 'package:buscafarma/core/widgets/terms_dialog.dart';

/// Service for managing Terms and Conditions acceptance and display logic
/// Handles first-time display detection and terms acceptance tracking
class TermsService {
  static const String _termsAcceptedKey = 'terms_and_conditions_accepted';
  static const String _termsVersionKey = 'terms_version_accepted';
  static const String _currentTermsVersion = '1.0';

  final LegalDocumentService _legalDocumentService;

  TermsService({LegalDocumentService? legalDocumentService}) : _legalDocumentService = legalDocumentService ?? LegalDocumentService();

  /// Checks if the user has accepted the current version of Terms and Conditions
  Future<bool> hasAcceptedTerms() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final hasAccepted = prefs.getBool(_termsAcceptedKey) ?? false;
      final acceptedVersion = prefs.getString(_termsVersionKey) ?? '';

      // User must have accepted terms AND the version must match current version
      final isCurrentVersion = acceptedVersion == _currentTermsVersion;

      debugPrint('TermsService: hasAccepted=$hasAccepted, acceptedVersion=$acceptedVersion, currentVersion=$_currentTermsVersion');
      return hasAccepted && isCurrentVersion;
    } catch (e) {
      // Graceful fallback - assume terms not accepted if SharedPreferences fails
      debugPrint('TermsService: Failed to check terms acceptance status: $e');
      return false;
    }
  }

  /// Marks the Terms and Conditions as accepted for the current version
  Future<void> markTermsAsAccepted() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_termsAcceptedKey, true);
      await prefs.setString(_termsVersionKey, _currentTermsVersion);
      debugPrint('TermsService: Terms marked as accepted for version $_currentTermsVersion');
    } catch (e) {
      // Non-blocking error - log but don't throw
      debugPrint('TermsService: Failed to mark terms as accepted: $e');
    }
  }

  /// Marks the Terms and Conditions as not accepted (for decline action)
  Future<void> markTermsAsDeclined() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_termsAcceptedKey, false);
      await prefs.remove(_termsVersionKey);
      debugPrint('TermsService: Terms marked as declined');
    } catch (e) {
      // Non-blocking error - log but don't throw
      debugPrint('TermsService: Failed to mark terms as declined: $e');
    }
  }

  /// Fetches Terms and Conditions content from the backend as plain text
  Future<String> getTermsContent() async {
    try {
      return await _legalDocumentService.getTermsAndConditionsPlainText();
    } catch (e) {
      debugPrint('TermsService: Failed to fetch terms content: $e');
      rethrow;
    }
  }

  /// Shows the Terms and Conditions dialog
  /// Returns true if user accepted, false if declined, null if dismissed
  Future<bool?> showTermsDialog(BuildContext context) async {
    try {
      // Check if context is still valid before showing dialog
      if (!context.mounted) return null;

      // Fetch terms content
      final termsContent = await getTermsContent();

      if (!context.mounted) return null;

      final result = await showDialog<bool>(
        context: context,
        barrierDismissible: false, // Prevent dismissal by tapping outside
        builder: (context) => TermsDialog(content: termsContent),
      );

      return result;
    } catch (e) {
      // Graceful fallback - log error but don't break app flow
      debugPrint('TermsService: Failed to show terms dialog: $e');
      return null;
    }
  }

  /// Shows Terms and Conditions for first-time users and handles acceptance
  /// Returns true if terms were shown and accepted, false otherwise
  Future<bool> showFirstTimeTerms(BuildContext context) async {
    final hasAccepted = await hasAcceptedTerms();
    if (hasAccepted) {
      debugPrint('TermsService: Terms already accepted, skipping display');
      return true; // Already accepted
    }

    // Check if context is still valid before showing dialog
    if (!context.mounted) return false;

    final result = await showTermsDialog(context);

    if (result == true) {
      await markTermsAsAccepted();
      return true;
    } else if (result == false) {
      await markTermsAsDeclined();
      return false;
    }

    // result is null (dialog dismissed somehow)
    return false;
  }

  /// Shows Terms and Conditions from profile section (always shows, regardless of acceptance status)
  /// Returns true if user accepted, false if declined, null if dismissed
  Future<bool?> showTermsFromProfile(BuildContext context) async {
    return await showTermsDialog(context);
  }

  /// Resets terms acceptance status (useful for testing or user logout)
  Future<void> resetTermsAcceptance() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_termsAcceptedKey);
      await prefs.remove(_termsVersionKey);
      debugPrint('TermsService: Terms acceptance status reset');
    } catch (e) {
      debugPrint('TermsService: Failed to reset terms acceptance: $e');
    }
  }

  /// Gets the current terms version
  String get currentTermsVersion => _currentTermsVersion;

  /// Checks if terms need to be shown (user hasn't accepted current version)
  Future<bool> shouldShowTerms() async {
    return !(await hasAcceptedTerms());
  }
}
