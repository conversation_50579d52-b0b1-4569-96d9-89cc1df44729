import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:buscafarma/core/utils/api_config.dart';
import 'package:buscafarma/core/error/failures.dart';

/// Service for fetching legal documents from the backend API
/// Handles HTML content retrieval with proper error handling
class LegalDocumentService {
  final Dio _dio;

  LegalDocumentService({Dio? dio}) : _dio = dio ?? Dio(BaseOptions(connectTimeout: Duration(seconds: ApiConfig.timeoutSeconds), receiveTimeout: Duration(seconds: ApiConfig.timeoutSeconds)));

  /// Fetches Terms and Conditions HTML content from the backend
  /// Returns the HTML content as a string
  /// Throws [LegalDocumentError] if the request fails
  Future<String> getTermsAndConditions() async {
    try {
      final url = '${ApiConfig.baseUrl.replaceAll('/api/v1', '')}/terms-of-service.html';
      debugPrint('LegalDocumentService: Fetching terms from $url');

      final response = await _dio.get(
        url,
        options: Options(
          headers: {'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8', 'Accept-Language': 'es-ES,es;q=0.9,en;q=0.8'},
          responseType: ResponseType.plain, // Get response as string
        ),
      );

      if (response.statusCode == 200 && response.data != null) {
        debugPrint('LegalDocumentService: Terms fetched successfully');
        return response.data as String;
      } else {
        throw const Failure.legalDocumentError('Invalid response from server');
      }
    } on DioException catch (e) {
      debugPrint('LegalDocumentService: DioException - ${e.message}');

      if (e.type == DioExceptionType.connectionTimeout || e.type == DioExceptionType.receiveTimeout) {
        throw const Failure.legalDocumentError('Connection timeout');
      } else if (e.type == DioExceptionType.connectionError) {
        throw const Failure.networkError();
      } else if (e.response?.statusCode == 404) {
        throw const Failure.legalDocumentError('Terms and Conditions not found');
      } else {
        throw Failure.legalDocumentError('Failed to load Terms and Conditions: ${e.message}');
      }
    } catch (e) {
      debugPrint('LegalDocumentService: Unexpected error - $e');
      throw Failure.legalDocumentError('Unexpected error: ${e.toString()}');
    }
  }

  /// Fetches Terms and Conditions plain text content from the backend
  /// Returns the plain text content as a string for mobile consumption
  /// Throws [LegalDocumentError] if the request fails
  Future<String> getTermsAndConditionsPlainText() async {
    try {
      final url = '${ApiConfig.baseUrl.replaceAll('/api/v1', '')}/terms-of-service.txt';
      debugPrint('LegalDocumentService: Fetching terms plain text from $url');

      final response = await _dio.get(
        url,
        options: Options(
          headers: {'Accept': 'text/plain,*/*;q=0.8', 'Accept-Language': 'es-ES,es;q=0.9,en;q=0.8'},
          responseType: ResponseType.plain, // Get response as string
        ),
      );

      if (response.statusCode == 200 && response.data != null) {
        debugPrint('LegalDocumentService: Terms plain text fetched successfully');
        return response.data as String;
      } else {
        throw const Failure.legalDocumentError('Invalid response from server');
      }
    } on DioException catch (e) {
      debugPrint('LegalDocumentService: DioException - ${e.message}');

      if (e.type == DioExceptionType.connectionTimeout || e.type == DioExceptionType.receiveTimeout) {
        throw const Failure.legalDocumentError('Connection timeout');
      } else if (e.type == DioExceptionType.connectionError) {
        throw const Failure.networkError();
      } else if (e.response?.statusCode == 404) {
        throw const Failure.legalDocumentError('Terms and Conditions not found');
      } else {
        throw Failure.legalDocumentError('Failed to load Terms and Conditions: ${e.message}');
      }
    } catch (e) {
      debugPrint('LegalDocumentService: Unexpected error - $e');
      throw Failure.legalDocumentError('Unexpected error: ${e.toString()}');
    }
  }

  /// Fetches Privacy Policy HTML content from the backend
  /// Returns the HTML content as a string
  /// Throws [LegalDocumentError] if the request fails
  Future<String> getPrivacyPolicy() async {
    try {
      final url = '${ApiConfig.baseUrl.replaceAll('/api/v1', '')}/privacy-policy.html';
      debugPrint('LegalDocumentService: Fetching privacy policy from $url');

      final response = await _dio.get(
        url,
        options: Options(
          headers: {'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8', 'Accept-Language': 'es-ES,es;q=0.9,en;q=0.8'},
          responseType: ResponseType.plain, // Get response as string
        ),
      );

      if (response.statusCode == 200 && response.data != null) {
        debugPrint('LegalDocumentService: Privacy policy fetched successfully');
        return response.data as String;
      } else {
        throw const Failure.legalDocumentError('Invalid response from server');
      }
    } on DioException catch (e) {
      debugPrint('LegalDocumentService: DioException - ${e.message}');

      if (e.type == DioExceptionType.connectionTimeout || e.type == DioExceptionType.receiveTimeout) {
        throw const Failure.legalDocumentError('Connection timeout');
      } else if (e.type == DioExceptionType.connectionError) {
        throw const Failure.networkError();
      } else if (e.response?.statusCode == 404) {
        throw const Failure.legalDocumentError('Privacy Policy not found');
      } else {
        throw Failure.legalDocumentError('Failed to load Privacy Policy: ${e.message}');
      }
    } catch (e) {
      debugPrint('LegalDocumentService: Unexpected error - $e');
      throw Failure.legalDocumentError('Unexpected error: ${e.toString()}');
    }
  }
}
