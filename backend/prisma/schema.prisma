// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

model user {
  id                 String   @id @map("_id")
  name               String
  email              String   @unique
  firebase_token     String
  current_location   Json?    // Point { latitude: number, longitude: number }
  preferences        Json     // UserPreferences
  health_data        Json     // HealthData
  profile_picture_url String?
  refreshToken      String?  // JWT refresh token
  medication_favorites medication_favorite[]
  data_deletion_requests data_deletion_request[]
}

model medication_favorite {
  id                String   @id @default(auto()) @map("_id") @db.ObjectId
  user_id           String
  user              user     @relation(fields: [user_id], references: [id], onDelete: Cascade)

  // Medication identification fields for deduplication
  cod_estab         String   // Establishment code
  cod_prod_e        Int      // Product code

  // Complete medication data from MINSA API
  medication_data   Json     // Full medication object

  // Metadata
  created_at        DateTime @default(now())
  updated_at        DateTime @updatedAt

  // Compound indexes for efficient querying
  @@index([user_id, created_at])
  @@unique([user_id, cod_estab, cod_prod_e])
}

model data_deletion_request {
  id                String   @id @default(auto()) @map("_id") @db.ObjectId
  user_id           String?
  user              user?    @relation(fields: [user_id], references: [id], onDelete: Cascade)

  // Request details
  email             String   // User email for verification
  status            String   // pending, processing, completed, cancelled, failed

  // Request metadata
  requested_at      DateTime @default(now())
  processed_at      DateTime?
  completed_at      DateTime?
  ip_address        String?
  user_agent        String?
  reason            String?

  // Data retention information
  data_retention_info Json   // Array of DataRetentionInfo objects

  // Admin notes
  admin_notes       String?

  // Timestamps
  created_at        DateTime @default(now())
  updated_at        DateTime @updatedAt

  // Indexes for efficient querying
  @@index([user_id, status])
  @@index([email, status])
  @@index([requested_at])
}