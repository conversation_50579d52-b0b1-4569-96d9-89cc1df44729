---
description: sirve para marcar las reglas de como se generaran los archivos del proyecto
globs: *.ts
---
You are tasked with developing a high-performance, scalable web application using a fully class-oriented, object-oriented design. The project must strictly adhere to the following guidelines and technologies:
	1.	Technology Stack
		•	TypeScript: Enforce type safety, modern object-oriented programming practices, and clear interfaces across all project components.
		•	Elysia.js: Utilize this modern web framework to build RESTful API endpoints and leverage its built-in schema validator for request validation.
		•	Bun.js: Employ Bun as the runtime environment for its exceptional performance, integrated bundling, transpiling, and package management.
		•	Prisma: Use Prisma as the ORM for interacting with the MongoDB database in a type-safe manner.
		•	MongoDB: Implement MongoDB as the NoSQL database for data persistence.
	2.	Architectural Layers and Responsibilities
		•	Development Layer:
		•	Contains configuration files, build scripts, environment settings, and developer tooling setups.
		•	Manages project-level settings and ensures all necessary build and runtime configurations are in place.
		•	Application Layer:
		•	Handles API route definitions, high-level request handling, and response formatting.
		•	Implements routing and request validation (using Elysia.js’s schema validator) to ensure that incoming data meets the required format before processing.
		•	Delegates business logic to the Domain Layer while serving as the integration point for incoming HTTP requests.
		•	Domain Layer:
		•	Encapsulates core business logic, domain models, and all domain-specific validations and rules.
		•	Remains independent from external libraries and frameworks to ensure testability and maintainability.
		•	Coordinates domain processes and business workflows without direct dependencies on infrastructure details.
		•	Infrastructure Layer:
		•	Responsible for data persistence, external service integrations, and low-level operations.
		•	Contains a dedicated controller folder where all class-based controllers are implemented. These controllers serve as intermediaries between the Application Layer’s routing and the Domain Layer’s business logic.
		•	Houses repository classes and other adapters that use Prisma to interact with MongoDB, isolating data access and persistence logic from business operations.
	3.	Integration and Implementation Guidelines
		•	Object-Oriented Design:
		•	Implement all components (controllers, services, repositories) as classes to enforce encapsulation and reusability.
		•	Utilize dependency injection through class constructors to decouple components and improve testability.
		•	Request Validation:
		•	Apply Elysia.js’s schema validator within the Application Layer to ensure that all incoming requests meet defined data schemas before being processed by class-based controllers.
		•	Separation of Concerns:
		•	Ensure the Application Layer focuses on handling requests and responses, while the Domain Layer handles all business logic.
		•	The Infrastructure Layer must only handle data persistence and integration with external systems, abstracting away any direct database operations from the Domain Layer.
		•	Error Handling and Logging:
		•	Implement robust error handling mechanisms in each layer to provide clear error messages and maintain system stability.
		•	Integrate logging practices that facilitate debugging and system monitoring across the entire application.
		•	Performance Optimization:
		•	Leverage Bun.js’s performance capabilities for efficient bundling, runtime execution, and resource management.
		•	Optimize class implementations to reduce overhead and ensure a responsive application.

Directory structure:
└── eminatarenx-clean-elysia/
    ├── README.md
    ├── Dockerfile
    ├── package.json
    ├── tsconfig.json
    ├── .dockerignore
    ├── prisma/
    │   ├── dev.db
    │   ├── dev.db-journal
    │   ├── schema.prisma
    │   └── migrations/
    │       ├── migration_lock.toml
    │       ├── 20240422180540_init/
    │       │   └── migration.sql
    │       └── 20240427225734_anadiendo_model_de_posts/
    │           └── migration.sql
    └── src/
        ├── index.ts
        ├── server/
        │   ├── depenncies.ts
        │   └── server.ts
        ├── services/
        │   ├── hash.ts
        │   ├── jwt.ts
        │   └── interfaces/
        │       ├── IHash.ts
        │       └── IJWT.ts
        └── user/
            ├── userRouter.ts
            ├── application/
            │   ├── create.ts
            │   └── login.ts
            ├── domain/
            │   ├── IUser.ts
            │   ├── User.ts
            │   └── userDTO.ts
            └── infrastructure/
                ├── UserRepository.ts
                └── controllers/
                    ├── createController.ts
                    └── login.ts


Files Content:

================================================
File: README.md
================================================
# Elysia with Bun runtime

## Getting Started

1. instalar todos los packetes
```bash
bun install
```
2. generar el cliente de prisma
```bash
bun run prisma generate
```
3. Iniciar la aplicacion
```bash
bun start
```

================================================
File: Dockerfile
================================================
FROM oven/bun:latest

RUN mkdir -p /app

WORKDIR /app

COPY . /app

RUN bun install

RUN bun run prisma generate

CMD ["bun", "start"]

================================================
File: package.json
================================================
{
  "name": "clean-api",
  "version": "1.0.50",
  "scripts": {
    "start": "bun run dist/src/index.js",
    "dev": "bun run --watch src/index.ts"
  },
  "dependencies": {
    "@aws-sdk/client-s3": "^3.564.0",
    "@prisma/client": "5.12.1",
    "bcrypt": "^5.1.1",
    "elysia": "latest",
    "jsonwebtoken": "^9.0.2"
  },
  "devDependencies": {
    "@types/bcrypt": "^5.0.2",
    "@types/jsonwebtoken": "^9.0.6",
    "bun-types": "latest",
    "prisma": "^5.12.1"
  },
  "module": "src/index.js"
}

================================================
File: tsconfig.json
================================================
{
  "compilerOptions": {

    "target": "ES2021",                                  /* Set the JavaScript language version for emitted JavaScript and include compatible library declarations. */

    "module": "ES2022",                                /* Specify what module code is generated. */
    "rootDir": "./",                                  /* Specify the root folder within your source files. */
    "moduleResolution": "node",                       /* Specify how TypeScript looks up a file from a given module specifier. */
    "baseUrl": "./",                                  /* Specify the base directory to resolve non-relative module names. */
    "outDir": "./dist",                          /* Specify multiple folders that act like './node_modules/@types'. */
    "types": ["bun-types"],                                      /* Specify type package names to be included without being referenced in a source file. */
          
    "esModuleInterop": true,                             /* Emit additional JavaScript to ease support for importing CommonJS modules. This enables 'allowSyntheticDefaultImports' for type compatibility. */
                      /* Disable resolving symlinks to their realpath. This correlates to the same flag in node. */
    "forceConsistentCasingInFileNames": true,            /* Ensure that casing is correct in imports. */

    "strict": true,                                      /* Enable all strict type-checking options. */

    "skipLibCheck": true                                 /* Skip type checking all .d.ts files. */
  }
}


================================================
File: .dockerignore
================================================
node_modules/

================================================
File: prisma/schema.prisma
================================================
// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}


model user {
  id String @id @default(uuid())
  email String @unique
  password String 
}


================================================
File: prisma/migrations/migration_lock.toml
================================================
# Please do not edit this file manually
# It should be added in your version-control system (i.e. Git)
provider = "sqlite"

================================================
File: prisma/migrations/20240422180540_init/migration.sql
================================================
-- CreateTable
CREATE TABLE "user" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "email" TEXT NOT NULL,
    "password" TEXT NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "user_email_key" ON "user"("email");


================================================
File: prisma/migrations/20240427225734_anadiendo_model_de_posts/migration.sql
================================================
-- CreateTable
CREATE TABLE "post" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "description" TEXT NOT NULL,
    "images" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    CONSTRAINT "post_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);


================================================
File: src/index.ts
================================================
import { Server } from "./server/server.js";

(() => {
    const server = new Server()
    server.start()
})();

================================================
File: src/server/depenncies.ts
================================================
import { UserRepository } from "../user/infrastructure/UserRepository.js";
import { CreateUser } from "../user/application/create.js";
import { CreateUserController } from "../user/infrastructure/controllers/createController.js";
import { Hash } from "../services/hash.js";
import { LoginController } from "../user/infrastructure/controllers/login.js";
import { LoginUser } from "../user/application/login.js";
import { JWT } from "../services/jwt.js";

const userRepository = new UserRepository();
const hashService = new Hash();
const jwtService = new JWT();

const createUser = new CreateUser(userRepository, hashService);
export const createUserController = new CreateUserController(createUser);

const loginUser = new LoginUser(userRepository, hashService, jwtService);
export const loginController = new LoginController(loginUser);


================================================
File: src/server/server.ts
================================================
import { Elysia } from 'elysia'
import { userRouter } from '../user/userRouter.js'

export class Server { 
    private app: Elysia

    constructor() {
        this.app = new Elysia()
        this.app.derive(({headers}) => {
            const auth = headers['authorization']
            return {
                token: auth?.startsWith('Bearer ') ? auth.slice(7): null
            }
        })
        this.app.group('/api/v1', (app) => app.use(userRouter))

    }

    public start() {
        this.app.listen(process.env.PORT || 3000, () => {
            console.log('Server is running on port 3000')
        })
    }
}

================================================
File: src/services/hash.ts
================================================
import bcrypt from 'bcrypt'
import type { IHash } from './interfaces/IHash.js'

export class Hash implements IHash {
    hash(password: string) {
        const salt = bcrypt.genSaltSync(10)
        return bcrypt.hash(password, salt)
    }

    compare(password: string, hash: string) {
        return bcrypt.compare(password, hash)
    }
}

================================================
File: src/services/jwt.ts
================================================
import type { IJWT } from "./interfaces/IJWT.js";
import jwt, { JwtPayload } from 'jsonwebtoken'

export class JWT implements IJWT {
    async sign(payload: string): Promise<string> {
        return jwt.sign({data: payload}, 'secret', { expiresIn: '1h'})
    }
    verify(token: string): JwtPayload{
        return jwt.verify(token, 'secret') as JwtPayload
    }
}



================================================
File: src/services/interfaces/IHash.ts
================================================
export interface IHash {
    hash(password: string): Promise<string>;
    compare(password: string, hash: string): Promise<boolean>;
}

================================================
File: src/services/interfaces/IJWT.ts
================================================
import { JwtPayload } from "jsonwebtoken";

export interface IJWT{
    sign(payload: string): Promise<string>;
    verify(token: string): JwtPayload;
}

================================================
File: src/user/userRouter.ts
================================================
import { Elysia, t } from 'elysia'
import { createUserController, loginController } from '../server/depenncies.js'
import { createUserDTO, loginUserDTO } from './domain/userDTO.js'
export const userRouter = new Elysia({prefix: '/users'})
    .post('/', createUserController.run.bind(createUserController),createUserDTO)
    .post('/login', loginController.run.bind(loginController),loginUserDTO)

================================================
File: src/user/application/create.ts
================================================
import type { IUser } from "../domain/IUser.js";
import type { IHash } from "../../services/interfaces/IHash.js";
export class CreateUser {
    constructor(
        private userRepository: IUser,
        private hash: IHash
    ){}

    async run(email: string, password: string) {
        const hashedPassword = await this.hash.hash(password)
        return await this.userRepository.create(email, hashedPassword);
    }
}

================================================
File: src/user/application/login.ts
================================================
import type { IUser } from "../domain/IUser.js";
import type { IHash } from "../../services/interfaces/IHash.js";
import type { IJWT } from "../../services/interfaces/IJWT.js";

export class LoginUser {
    constructor(
        private userRepository: IUser,
        private hash: IHash,
        private jwt: IJWT
    ){}

    async run(email: string, password: string) {
        const user = await this.userRepository.find(email);
        if (!user) throw new Error('User not found');
        const isValid = this.hash.compare(password, user.password);
        if(!isValid) throw new Error('Invalid password');  
        const token = await this.jwt.sign(user.id);
        user.setToken(token);
        return user;
    }
}


================================================
File: src/user/domain/IUser.ts
================================================
import { User } from "./User.js";

export interface IUser {
    create(email: string, password: string): Promise<User>;
    find(email: string): Promise<User | null>;
}

================================================
File: src/user/domain/User.ts
================================================
export class User {
    constructor(
        public id: string,
        public email: string,
        public password: string,
        public token?: string,
    ){}

    setToken(token: string){
        this.token = token;
    }
}

================================================
File: src/user/domain/userDTO.ts
================================================
import {t } from 'elysia'

export const createUserDTO =  {
    body: t.Object({
        email: t.String(),
        password: t.String()
    })
}

export const loginUserDTO =  {
    body: t.Object({
        email: t.String(),
        password: t.String()
    })
}

================================================
File: src/user/infrastructure/UserRepository.ts
================================================
import type { IUser } from "../domain/IUser.js";
import { User } from "../domain/User.js";
import { PrismaClient } from '@prisma/client';

export class UserRepository implements IUser {
    private db: PrismaClient
    constructor() {
        this.db = new PrismaClient();
    }
    async create(email: string, password: string): Promise<User> {
        const user = await this.db.user.create({
            data: {
                email, 
                password
            }
        })

        return new User(user.id, user.email, user.password);
    }

    async find(email: string): Promise<User | null> {
        const user = await this.db.user.findUnique({
            where: {
                email
            }
        })

        if (!user) {
            return null;
        }

        return new User(user.id, user.email, user.password);
    }
}

================================================
File: src/user/infrastructure/controllers/createController.ts
================================================
import { CreateUser } from "../../application/create.js";


export class CreateUserController { 
    constructor(
        private createUser: CreateUser
    ){}

    async run({body}:any){
        try {

            const user = await this.createUser.run(body.email, body.password);
            return {
                status: 200,
                data: user
            }
        } catch (e) {
            const error = e as Error;
            return {
                status: 500,
                message: error.message
            }
        }
    }
}

================================================
File: src/user/infrastructure/controllers/login.ts
================================================
import { LoginUser } from "../../application/login.js";
type Login = {
    body: {
        email: string,
        password: string
    },
    token: string
}
export class LoginController {
    constructor(
        private loginUser: LoginUser
    ){}
    async run({body}: Login) {

        try {
            const user =  await this.loginUser.run(body.email, body.password);
            return {
                code: 200,
                user,
                message: 'User logged in'
            }
        } catch (error) {
            const err = error as Error
            return {
                code: 400,
                message: err.message
            }
        }
    }
}

