#!/usr/bin/env bun

/**
 * Email Testing Script for BuscaFarma Data Deletion System
 *
 * This script tests the email configuration and sends a test email
 * to verify that the Gmail SMTP setup is working correctly.
 *
 * Usage:
 *   bun run src/scripts/test-email.ts [recipient-email]
 *
 * Environment Variables Required:
 *   EMAIL_USER - Gmail address
 *   EMAIL_PASS - Gmail App Password (16 characters)
 *   ADMIN_EMAIL - Admin email for notifications (optional)
 */

import { EmailService } from '../services/EmailService.js'

async function testEmailConfiguration() {
  console.log('🧪 Testing BuscaFarma Email Configuration...\n')

  // Check environment variables
  const emailUser = process.env.EMAIL_USER
  const emailPass = process.env.EMAIL_PASS
  const adminEmail = process.env.ADMIN_EMAIL

  console.log('📋 Environment Configuration:')
  console.log(`   EMAIL_USER: ${emailUser ? '✅ Set' : '❌ Missing'}`)
  console.log(`   EMAIL_PASS: ${emailPass ? '✅ Set' : '❌ Missing'}`)
  console.log(`   ADMIN_EMAIL: ${adminEmail ? '✅ Set' : '⚠️ Not set (optional)'}`)
  console.log(`   NODE_ENV: ${process.env.NODE_ENV || 'development'}`)
  console.log()

  if (!emailUser || !emailPass) {
    console.error('❌ Missing required email configuration!')
    console.error('💡 Please set EMAIL_USER and EMAIL_PASS environment variables')
    console.error('💡 For Gmail setup instructions, see EmailService.ts documentation')
    process.exit(1)
  }

  // Create email service configuration
  const emailConfig = {
    host: process.env.EMAIL_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.EMAIL_PORT || '587'),
    secure: process.env.EMAIL_SECURE === 'true',
    auth: {
      user: emailUser,
      pass: emailPass
    },
    from: process.env.EMAIL_FROM || emailUser,
    service: 'gmail',
    tls: {
      rejectUnauthorized: false
    },
    forceEmailSending: true // Force email sending for testing
  }

  const emailService = new EmailService(emailConfig)

  // Test connection
  console.log('🔗 Testing Gmail SMTP connection...')
  const connectionOk = await emailService.verifyConnection()

  if (!connectionOk) {
    console.error('❌ Email connection failed!')
    console.error('💡 Check your Gmail credentials and App Password')
    process.exit(1)
  }

  // Get recipient email from command line or use sender email
  const recipientEmail = process.argv[2] || emailUser
  console.log(`📧 Sending test email to: ${recipientEmail}`)

  // Test deletion confirmation email
  console.log('\n📨 Testing deletion confirmation email...')
  try {
    const confirmationSent = await emailService.sendDeletionRequestConfirmation(recipientEmail, 'Test User', 'test-request-id-123')

    if (confirmationSent) {
      console.log('✅ Deletion confirmation email sent successfully!')
    } else {
      console.log('❌ Deletion confirmation email failed to send')
    }
  } catch (error) {
    console.error('❌ Error sending deletion confirmation email:', error)
  }

  // Test admin notification email (if admin email is configured)
  if (adminEmail) {
    console.log('\n📨 Testing admin notification email...')
    try {
      const adminNotificationSent = await emailService.sendAdminDeletionNotification(adminEmail, recipientEmail, 'test-request-id-123')

      if (adminNotificationSent) {
        console.log('✅ Admin notification email sent successfully!')
      } else {
        console.log('❌ Admin notification email failed to send')
      }
    } catch (error) {
      console.error('❌ Error sending admin notification email:', error)
    }
  } else {
    console.log('\n⚠️ Skipping admin notification test (ADMIN_EMAIL not set)')
  }

  // Test completion notification email
  console.log('\n📨 Testing completion notification email...')
  try {
    const completionSent = await emailService.sendDeletionCompletedNotification(recipientEmail, 'Test User', 'test-request-id-123')

    if (completionSent) {
      console.log('✅ Completion notification email sent successfully!')
    } else {
      console.log('❌ Completion notification email failed to send')
    }
  } catch (error) {
    console.error('❌ Error sending completion notification email:', error)
  }

  console.log('\n🎉 Email testing completed!')
  console.log('💡 Check your inbox for the test emails')
  console.log('💡 If emails are not received, check your spam folder')
}

// Run the test
testEmailConfiguration().catch((error) => {
  console.error('💥 Email test failed:', error)
  process.exit(1)
})
