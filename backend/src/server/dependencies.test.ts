import { describe, expect, it } from 'bun:test'

describe('Server Dependencies', () => {
  describe('Dependency exports', () => {
    it('should export JWT service', async () => {
      const { JWT } = await import('../services/jwt.js')
      expect(JWT).toBeDefined()
      expect(typeof JWT).toBe('function')
    })

    it('should export user-related dependencies', async () => {
      const { LoginUser } = await import('../user/application/login.js')
      const { UserRepository } = await import('../user/infrastructure/UserRepository.js')
      const { LoginController } = await import('../user/infrastructure/controllers/login.js')

      expect(LoginUser).toBeDefined()
      expect(UserRepository).toBeDefined()
      expect(LoginController).toBeDefined()

      expect(typeof LoginUser).toBe('function')
      expect(typeof UserRepository).toBe('function')
      expect(typeof LoginController).toBe('function')
    })

    it('should export favorites-related dependencies', async () => {
      const { AddMedicationFavorite } = await import('../favorites/application/AddMedicationFavorite.js')
      const { ListMedicationFavorites } = await import('../favorites/application/ListMedicationFavorites.js')
      const { RemoveMedicationFavorite } = await import('../favorites/application/RemoveMedicationFavorite.js')
      const { SearchMedicationFavorites } = await import('../favorites/application/SearchMedicationFavorites.js')
      const { MedicationFavoriteRepository } = await import('../favorites/infrastructure/MedicationFavoriteRepository.js')
      const { MedicationFavoriteController } = await import('../favorites/infrastructure/controllers/MedicationFavoriteController.js')

      expect(AddMedicationFavorite).toBeDefined()
      expect(ListMedicationFavorites).toBeDefined()
      expect(RemoveMedicationFavorite).toBeDefined()
      expect(SearchMedicationFavorites).toBeDefined()
      expect(MedicationFavoriteRepository).toBeDefined()
      expect(MedicationFavoriteController).toBeDefined()
    })

    it('should export legal-related dependencies', async () => {
      const { GetLegalDocument } = await import('../legal/application/GetLegalDocument.js')
      const { LegalController } = await import('../legal/infrastructure/controllers/LegalController.js')

      expect(GetLegalDocument).toBeDefined()
      expect(LegalController).toBeDefined()

      expect(typeof GetLegalDocument).toBe('function')
      expect(typeof LegalController).toBe('function')
    })
  })

  describe('Dependency instantiation', () => {
    it('should create JWT service instance', async () => {
      const { JWT } = await import('../services/jwt.js')
      const jwtService = new JWT()

      expect(jwtService).toBeDefined()
      expect(typeof jwtService.sign).toBe('function')
      expect(typeof jwtService.verify).toBe('function')
      expect(typeof jwtService.verifyRefresh).toBe('function')
    })

    it('should create legal service instances', async () => {
      const { GetLegalDocument } = await import('../legal/application/GetLegalDocument.js')
      const { GetDataDeletionForm } = await import('../legal/application/GetDataDeletionForm.js')
      const { LegalController } = await import('../legal/infrastructure/controllers/LegalController.js')

      const getLegalDocument = new GetLegalDocument()
      const getDataDeletionForm = new GetDataDeletionForm()
      const legalController = new LegalController(getLegalDocument, getDataDeletionForm)

      expect(getLegalDocument).toBeDefined()
      expect(getDataDeletionForm).toBeDefined()
      expect(legalController).toBeDefined()
      expect(typeof getLegalDocument.execute).toBe('function')
      expect(typeof getDataDeletionForm.execute).toBe('function')
      expect(typeof legalController.getPrivacyPolicy).toBe('function')
      expect(typeof legalController.getLegalDocument).toBe('function')
      expect(typeof legalController.getDataDeletionForm).toBe('function')
    })
  })

  describe('Dependency injection pattern', () => {
    it('should follow constructor injection pattern for controllers', async () => {
      const { GetLegalDocument } = await import('../legal/application/GetLegalDocument.js')
      const { GetDataDeletionForm } = await import('../legal/application/GetDataDeletionForm.js')
      const { LegalController } = await import('../legal/infrastructure/controllers/LegalController.js')

      const getLegalDocumentService = new GetLegalDocument()
      const getDataDeletionFormService = new GetDataDeletionForm()
      const controller = new LegalController(getLegalDocumentService, getDataDeletionFormService)

      // Test that the controller can use the injected service
      const result = await controller.getPrivacyPolicy()
      expect(result).toBeDefined()
      expect(result.status).toBe(200)
      expect(result.data).toBeDefined()
    })

    it('should maintain service dependencies', async () => {
      const { JWT } = await import('../services/jwt.js')
      const { LoginUser } = await import('../user/application/login.js')

      // Test that services can be instantiated with their dependencies
      const jwtService = new JWT()
      expect(jwtService).toBeDefined()

      // LoginUser requires UserRepository and JWT service
      // This tests that the dependency structure is correct
      expect(typeof LoginUser).toBe('function')
    })
  })

  describe('Module imports', () => {
    it('should import all required modules without errors', async () => {
      // Test that all dependency modules can be imported
      const imports = await Promise.all([
        import('../services/jwt.js'),
        import('../user/application/login.js'),
        import('../user/infrastructure/UserRepository.js'),
        import('../user/infrastructure/controllers/login.js'),
        import('../favorites/application/AddMedicationFavorite.js'),
        import('../favorites/application/ListMedicationFavorites.js'),
        import('../favorites/application/RemoveMedicationFavorite.js'),
        import('../favorites/application/SearchMedicationFavorites.js'),
        import('../favorites/infrastructure/MedicationFavoriteRepository.js'),
        import('../favorites/infrastructure/controllers/MedicationFavoriteController.js'),
        import('../legal/application/GetLegalDocument.js'),
        import('../legal/infrastructure/controllers/LegalController.js')
      ])

      // All imports should succeed
      imports.forEach((module) => {
        expect(module).toBeDefined()
      })
    })

    it('should have consistent export patterns', async () => {
      // Test that all main classes are exported as named exports
      const modules = [
        await import('../services/jwt.js'),
        await import('../user/application/login.js'),
        await import('../legal/application/GetLegalDocument.js'),
        await import('../legal/infrastructure/controllers/LegalController.js')
      ]

      modules.forEach((module) => {
        const exports = Object.keys(module)
        expect(exports.length).toBeGreaterThan(0)
      })
    })
  })
})
