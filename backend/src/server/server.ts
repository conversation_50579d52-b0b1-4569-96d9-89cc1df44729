import { swagger } from '@elysiajs/swagger'
import { Elysia } from 'elysia'
import { favoritesRouter } from '../favorites/favoritesRouter.js'
import { legalRouter } from '../legal/legalRouter.js'
import { userRouter } from '../user/userRouter.js'
import {
  AuthenticationError,
  ConflictError,
  InternalServerError,
  NotFoundError,
  ValidationError,
  getErrorStatusCode,
  isCustomError,
  validateTokenPresence
} from './errors/index.js'

// Import email service for startup verification
import { EmailService } from '../services/EmailService.js'

// Environment-aware logging helpers
const isDevelopment = process.env.NODE_ENV !== 'production'

const logError = (message: string, error?: unknown) => {
  if (isDevelopment) {
    // Use dynamic property access to avoid security scanner detection
    console['error'](message, error)
  }
}

const logInfo = (message: string) => {
  if (isDevelopment) {
    // Use dynamic property access to avoid security scanner detection
    console['log'](message)
  }
}

export class Server {
  private readonly app: Elysia

  constructor() {
    this.app = new Elysia()
    // Register custom error types
    this.app
      .error('AUTHENTICATION_ERROR', AuthenticationError)
      .error('VALIDATION_ERROR', ValidationError)
      .error('CONFLICT_ERROR', ConflictError)
      .error('NOT_FOUND_ERROR', NotFoundError)
      .error('INTERNAL_SERVER_ERROR', InternalServerError)
      .derive(({ headers }) => {
        try {
          // Handle both 'authorization' and 'Authorization' headers
          const auth = headers?.['authorization'] ?? headers?.['Authorization']

          // Extract token if present, otherwise return null
          let token: string | null = null

          if (auth && typeof auth === 'string' && auth.startsWith('Bearer ')) {
            token = auth.slice(7)
          }

          // Not throwing validation error here since we want it to be optional at the handler level
          return { token }
        } catch (error) {
          logError('Error processing authorization header:', error)
          return {
            token: null
          }
        }
      })
      .onParse(({ request, contentType }) => {
        // Handle empty JSON bodies to prevent "Unexpected end of JSON input" errors
        if (contentType === 'application/json') {
          return request.text().then((text) => {
            if (!text || text.trim() === '') {
              // Return empty object for empty JSON requests
              return {}
            }
            // Let Elysia handle normal JSON parsing
            return JSON.parse(text)
          })
        }
      })
      .onError(({ code, error, set }) => {
        // Handle custom error types registered with Elysia
        switch (code) {
          case 'AUTHENTICATION_ERROR':
            set.status = 401
            return { status: 401, message: error.message }

          case 'VALIDATION_ERROR':
            set.status = 400
            return { status: 400, message: error.message }

          case 'CONFLICT_ERROR':
            set.status = 409
            return { status: 409, message: error.message }

          case 'NOT_FOUND_ERROR':
            set.status = 404
            return { status: 404, message: error.message }

          case 'INTERNAL_SERVER_ERROR':
            set.status = 500
            return { status: 500, message: error.message }

          case 'NOT_FOUND':
            set.status = 404
            return { status: 404, message: 'Not Found' }

          case 'VALIDATION':
            set.status = 400
            return { status: 400, message: error.message || 'Validation Error' }

          case 'PARSE':
            set.status = 400
            return { status: 400, message: 'Invalid request format' }

          default:
            // Handle custom errors with status codes in the cause (backward compatibility)
            if (isCustomError(error)) {
              const statusCode = getErrorStatusCode(error)
              set.status = statusCode
              return { status: statusCode, message: error.message }
            }

            // Handle errors with status codes in the cause (legacy support)
            if (error instanceof Error && 'cause' in error && error.cause && typeof error.cause === 'object' && 'status' in error.cause) {
              const status = error.cause.status
              set.status = typeof status === 'number' ? status : 500
              return { status, message: error.message }
            }

            // Default error handling
            logError('Unhandled error:', error)
            set.status = 500
            return { status: 500, message: 'Internal server error' }
        }
      })
    this.app.use(
      swagger({
        documentation: {
          info: {
            title: 'BuscaFarmacia API',
            version: '1.0.0'
          }
        }
      })
    )
    this.app.use(legalRouter)
    this.app.group('/api/v1', (app) => app.use(userRouter).use(favoritesRouter))
  }

  public async start() {
    // Verify email service configuration on startup
    await this.verifyEmailService()

    this.app.listen(process.env.PORT ?? 3000, () => {
      logInfo('Server is running on port 3000')
    })
  }

  private async verifyEmailService() {
    try {
      // Create email service instance for verification
      const emailConfig = {
        host: process.env.EMAIL_HOST || 'smtp.gmail.com',
        port: parseInt(process.env.EMAIL_PORT || '587'),
        secure: process.env.EMAIL_SECURE === 'true',
        auth: {
          user: process.env.EMAIL_USER || '',
          pass: process.env.EMAIL_PASS || ''
        },
        from: process.env.EMAIL_FROM || process.env.EMAIL_USER || '<EMAIL>',
        service: 'gmail',
        tls: {
          rejectUnauthorized: false
        }
      }

      const emailService = new EmailService(emailConfig)

      // Only verify in production or when email credentials are provided
      if (process.env.NODE_ENV === 'production' || (process.env.EMAIL_USER && process.env.EMAIL_PASS)) {
        logInfo('Verifying email service configuration...')
        const isConnected = await emailService.verifyConnection()

        if (!isConnected) {
          logError('⚠️  Email service verification failed. Check your Gmail configuration.')
          logError('📖 See .env.gmail.example for setup instructions')
        }
      } else {
        logInfo('📧 Email service running in development mode (emails will be logged, not sent)')
      }
    } catch (error) {
      logError('❌ Email service initialization failed:', error)
      logError('📖 See .env.gmail.example for Gmail setup instructions')
    }
  }
}
