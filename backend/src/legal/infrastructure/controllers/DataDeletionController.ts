import { InternalServerError, ValidationError, isCustomError } from '../../../server/errors/index.js'
import { type DeletionRequestInput, ProcessDataDeletionRequest } from '../../application/ProcessDataDeletionRequest.js'

export interface DeletionRequestBody {
  email: string
  reason?: string
  confirmDeletion: boolean
  confirmIdentity: boolean
}

export interface RequestContext {
  body: DeletionRequestBody
  headers?: Record<string, string>
  ip?: string
}

export class DataDeletionController {
  constructor(private readonly processDataDeletionRequestService: ProcessDataDeletionRequest) {}

  async createDeletionRequest(context: RequestContext) {
    try {
      // Validate required fields
      if (!context.body.email) {
        throw new ValidationError('Email is required')
      }

      if (!context.body.confirmDeletion) {
        throw new ValidationError('You must confirm that you understand the deletion consequences')
      }

      if (!context.body.confirmIdentity) {
        throw new ValidationError('You must confirm that you are the account owner')
      }

      // Extract request metadata
      const input: DeletionRequestInput = {
        email: context.body.email.toLowerCase().trim(),
        reason: context.body.reason,
        ipAddress: context.ip || this.extractIpFromHeaders(context.headers),
        userAgent: context.headers?.['user-agent'] || context.headers?.['User-Agent']
      }

      // Create the deletion request
      const deletionRequest = await this.processDataDeletionRequestService.createRequest(input)

      return {
        status: 201,
        data: {
          success: true,
          message: 'Deletion request created successfully',
          requestId: deletionRequest.id,
          email: deletionRequest.email,
          status: deletionRequest.status,
          estimatedCompletion: deletionRequest.getEstimatedCompletionTime(),
          dataRetentionInfo: deletionRequest.dataRetentionInfo
        }
      }
    } catch (error) {
      // Handle custom errors with proper status codes
      if (isCustomError(error)) {
        throw error
      }

      // Handle unexpected errors
      throw new InternalServerError((error as Error).message)
    }
  }

  async getRequestStatus(requestId: string) {
    try {
      if (!requestId) {
        throw new ValidationError('Request ID is required')
      }

      const deletionRequest = await this.processDataDeletionRequestService.getRequestStatus(requestId)

      return {
        status: 200,
        data: {
          requestId: deletionRequest.id,
          email: deletionRequest.email,
          status: deletionRequest.status,
          requestedAt: deletionRequest.metadata.requestedAt,
          processedAt: deletionRequest.metadata.processedAt,
          completedAt: deletionRequest.metadata.completedAt,
          estimatedCompletion: deletionRequest.getEstimatedCompletionTime(),
          dataRetentionInfo: deletionRequest.dataRetentionInfo
        }
      }
    } catch (error) {
      // Handle custom errors with proper status codes
      if (isCustomError(error)) {
        throw error
      }

      // Handle unexpected errors
      throw new InternalServerError((error as Error).message)
    }
  }

  async getRequestsByEmail(email: string) {
    try {
      if (!email) {
        throw new ValidationError('Email is required')
      }

      const deletionRequests = await this.processDataDeletionRequestService.getRequestsByEmail(email)

      return {
        status: 200,
        data: {
          email,
          requests: deletionRequests.map((request) => ({
            requestId: request.id,
            status: request.status,
            requestedAt: request.metadata.requestedAt,
            processedAt: request.metadata.processedAt,
            completedAt: request.metadata.completedAt,
            estimatedCompletion: request.getEstimatedCompletionTime(),
            reason: request.metadata.reason
          }))
        }
      }
    } catch (error) {
      // Handle custom errors with proper status codes
      if (isCustomError(error)) {
        throw error
      }

      // Handle unexpected errors
      throw new InternalServerError((error as Error).message)
    }
  }

  // Admin methods for processing requests
  async processRequest(requestId: string, adminNotes?: string) {
    try {
      if (!requestId) {
        throw new ValidationError('Request ID is required')
      }

      const deletionRequest = await this.processDataDeletionRequestService.processRequest(requestId, adminNotes)

      return {
        status: 200,
        data: {
          success: true,
          message: 'Request marked as processing',
          requestId: deletionRequest.id,
          status: deletionRequest.status,
          processedAt: deletionRequest.metadata.processedAt
        }
      }
    } catch (error) {
      // Handle custom errors with proper status codes
      if (isCustomError(error)) {
        throw error
      }

      // Handle unexpected errors
      throw new InternalServerError((error as Error).message)
    }
  }

  async completeRequest(requestId: string, adminNotes?: string) {
    try {
      if (!requestId) {
        throw new ValidationError('Request ID is required')
      }

      const deletionRequest = await this.processDataDeletionRequestService.completeRequest(requestId, adminNotes)

      return {
        status: 200,
        data: {
          success: true,
          message: 'Request completed successfully',
          requestId: deletionRequest.id,
          status: deletionRequest.status,
          completedAt: deletionRequest.metadata.completedAt
        }
      }
    } catch (error) {
      // Handle custom errors with proper status codes
      if (isCustomError(error)) {
        throw error
      }

      // Handle unexpected errors
      throw new InternalServerError((error as Error).message)
    }
  }

  async cancelRequest(requestId: string, adminNotes?: string) {
    try {
      if (!requestId) {
        throw new ValidationError('Request ID is required')
      }

      const deletionRequest = await this.processDataDeletionRequestService.cancelRequest(requestId, adminNotes)

      return {
        status: 200,
        data: {
          success: true,
          message: 'Request cancelled successfully',
          requestId: deletionRequest.id,
          status: deletionRequest.status
        }
      }
    } catch (error) {
      // Handle custom errors with proper status codes
      if (isCustomError(error)) {
        throw error
      }

      // Handle unexpected errors
      throw new InternalServerError((error as Error).message)
    }
  }

  private extractIpFromHeaders(headers?: Record<string, string>): string | undefined {
    if (!headers) return undefined

    // Check common headers for client IP
    const ipHeaders = ['x-forwarded-for', 'x-real-ip', 'x-client-ip', 'cf-connecting-ip', 'x-forwarded', 'forwarded-for', 'forwarded']

    for (const header of ipHeaders) {
      const value = headers[header] || headers[header.toUpperCase()]
      if (value) {
        // x-forwarded-for can contain multiple IPs, take the first one
        return value.split(',')[0].trim()
      }
    }

    return undefined
  }
}
