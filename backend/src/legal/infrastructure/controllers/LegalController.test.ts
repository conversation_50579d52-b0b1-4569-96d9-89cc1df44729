import { afterEach, beforeEach, describe, expect, it, mock } from 'bun:test'
import { InternalServerError, NotFoundError } from '../../../server/errors/index.js'
import type { GetDataDeletionForm } from '../../application/GetDataDeletionForm.js'
import type { GetLegalDocument } from '../../application/GetLegalDocument.js'
import { LegalDocument } from '../../domain/LegalDocument.js'
import { LegalController } from './LegalController.js'

// Mock types for better type safety
interface MockGetLegalDocument {
  execute: ReturnType<typeof mock>
}

interface MockGetDataDeletionForm {
  execute: ReturnType<typeof mock>
}

describe('LegalController', () => {
  let legalController: LegalController
  let mockGetLegalDocument: MockGetLegalDocument
  let mockGetDataDeletionForm: MockGetDataDeletionForm

  beforeEach(() => {
    mockGetLegalDocument = {
      execute: mock()
    }

    mockGetDataDeletionForm = {
      execute: mock()
    }

    legalController = new LegalController(
      mockGetLegalDocument as unknown as GetLegalDocument,
      mockGetDataDeletionForm as unknown as GetDataDeletionForm
    )
  })

  afterEach(() => {
    // Reset mocks after each test
    mockGetLegalDocument.execute.mockReset()
    mockGetDataDeletionForm.execute.mockReset()
  })

  describe('getPrivacyPolicy', () => {
    it('should return privacy policy HTML successfully', async () => {
      const mockDocument = new LegalDocument('privacy-policy-v1', 'privacy-policy', 'Test Privacy Policy', '<p>Test content</p>', {
        lastUpdated: new Date('2024-12-28'),
        version: '1.0',
        language: 'es'
      })

      mockGetLegalDocument.execute.mockResolvedValue(mockDocument)

      const result = await legalController.getPrivacyPolicy()

      expect(mockGetLegalDocument.execute).toHaveBeenCalledWith('privacy-policy')
      expect(result.status).toBe(200)
      expect(result.data).toContain('<!DOCTYPE html>')
      expect(result.data).toContain('Test Privacy Policy')
      expect(result.data).toContain('<p>Test content</p>')
    })

    it('should throw InternalServerError when GetLegalDocument fails', async () => {
      mockGetLegalDocument.execute.mockRejectedValue(new Error('Database error'))

      await expect(legalController.getPrivacyPolicy()).rejects.toThrow('Database error')
    })
  })

  describe('getLegalDocument', () => {
    it('should return legal document HTML successfully', async () => {
      const mockDocument = new LegalDocument('terms-v1', 'terms-of-service', 'Test Terms', '<p>Terms content</p>', {
        lastUpdated: new Date('2024-12-28'),
        version: '1.0',
        language: 'es'
      })

      mockGetLegalDocument.execute.mockResolvedValue(mockDocument)

      const result = await legalController.getLegalDocument('terms-of-service')

      expect(mockGetLegalDocument.execute).toHaveBeenCalledWith('terms-of-service')
      expect(result.status).toBe(200)
      expect(result.data).toContain('<!DOCTYPE html>')
      expect(result.data).toContain('Test Terms')
    })

    it('should return 404 HTML when document is not implemented', async () => {
      mockGetLegalDocument.execute.mockRejectedValue(new NotFoundError('Terms of service not implemented yet'))

      const result = await legalController.getLegalDocument('terms-of-service')

      expect(result.status).toBe(404)
      expect(result.data).toContain('<!DOCTYPE html>')
      expect(result.data).toContain('Documento no encontrado')
      expect(result.data).toContain('terms-of-service')
    })

    it('should return 404 HTML when document type is unknown', async () => {
      mockGetLegalDocument.execute.mockRejectedValue(new NotFoundError('Unknown document type: invalid-type'))

      const result = await legalController.getLegalDocument('invalid-type' as any)

      expect(result.status).toBe(404)
      expect(result.data).toContain('Documento no encontrado')
      expect(result.data).toContain('invalid-type')
    })

    it('should throw InternalServerError for unexpected errors', async () => {
      mockGetLegalDocument.execute.mockRejectedValue(new Error('Unexpected database error'))

      await expect(legalController.getLegalDocument('privacy-policy')).rejects.toThrow('Unexpected database error')
    })
  })

  describe('getDataDeletionForm', () => {
    it('should return data deletion form HTML successfully', async () => {
      const mockDocument = new LegalDocument(
        'data-deletion-form',
        'data-deletion',
        'BuscaFarma - Solicitud de Eliminación de Cuenta',
        '<div>Data deletion form content</div>',
        {
          lastUpdated: new Date('2024-12-28'),
          version: '1.0',
          language: 'es'
        }
      )

      mockGetDataDeletionForm.execute.mockReturnValue(mockDocument)

      const result = await legalController.getDataDeletionForm()

      expect(mockGetDataDeletionForm.execute).toHaveBeenCalled()
      expect(result.status).toBe(200)
      expect(result.data).toContain('<!DOCTYPE html>')
      expect(result.data).toContain('BuscaFarma - Solicitud de Eliminación de Cuenta')
      expect(result.data).toContain('<div>Data deletion form content</div>')
    })

    it('should throw InternalServerError when GetDataDeletionForm fails', async () => {
      mockGetDataDeletionForm.execute.mockImplementation(() => {
        throw new Error('Form generation error')
      })

      await expect(legalController.getDataDeletionForm()).rejects.toThrow('Form generation error')
    })
  })
})
