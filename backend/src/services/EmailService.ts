import nodemailer from 'nodemailer'
import type { Transporter } from 'nodemailer'

/**
 * Email configuration interface for Gmail SMTP
 *
 * Gmail SMTP Configuration Requirements:
 * - Host: smtp.gmail.com
 * - Port: 587 (TLS) or 465 (SSL)
 * - Security: TLS recommended (secure: false with port 587)
 * - Authentication: Gmail email and App Password (not regular password)
 *
 * To set up Gmail App Password:
 * 1. Enable 2-Factor Authentication on your Gmail account
 * 2. Go to Google Account settings > Security > App passwords
 * 3. Generate a new app password for "Mail"
 * 4. Use this 16-character password (not your regular Gmail password)
 *
 * Required Environment Variables:
 * - EMAIL_HOST=smtp.gmail.com
 * - EMAIL_PORT=587
 * - EMAIL_SECURE=false
 * - EMAIL_USER=<EMAIL>
 * - EMAIL_PASS=your-16-character-app-password
 * - EMAIL_FROM=<EMAIL> (or custom from address)
 */
export interface EmailConfig {
  host: string
  port: number
  secure: boolean
  auth: {
    user: string
    pass: string
  }
  from: string
  // Gmail-specific options
  service?: string
  tls?: {
    rejectUnauthorized: boolean
  }
  // Force email sending even in development mode
  forceEmailSending?: boolean
}

export interface EmailTemplate {
  subject: string
  html: string
  text: string
}

export class EmailService {
  private config: EmailConfig
  private transporter: Transporter

  constructor(config: EmailConfig) {
    this.config = config

    // Gmail-optimized transporter configuration
    const transportConfig: {
      host: string
      port: number
      secure: boolean
      auth: {
        user: string
        pass: string
      }
      tls: {
        rejectUnauthorized: boolean
        ciphers: string
      }
      connectionTimeout: number
      greetingTimeout: number
      socketTimeout: number
      service?: string
    } = {
      host: config.host,
      port: config.port,
      secure: config.secure, // false for 587, true for 465
      auth: {
        user: config.auth.user,
        pass: config.auth.pass
      },
      // Gmail-specific TLS configuration
      tls: {
        rejectUnauthorized: false, // Allow self-signed certificates
        ciphers: 'SSLv3' // Support older SSL versions if needed
      },
      // Connection timeout and retry settings
      connectionTimeout: 60000, // 60 seconds
      greetingTimeout: 30000, // 30 seconds
      socketTimeout: 60000 // 60 seconds
    }

    // If using Gmail service, add service identifier
    if (config.host === 'smtp.gmail.com' || config.service === 'gmail') {
      transportConfig.service = 'gmail'
    }

    this.transporter = nodemailer.createTransport(transportConfig)
  }

  /**
   * Verify the email configuration and connection to Gmail
   * Call this method during application startup to ensure email is properly configured
   */
  async verifyConnection(): Promise<boolean> {
    try {
      await this.transporter.verify()
      return true
    } catch (_error) {
      return false
    }
  }

  async sendDeletionRequestConfirmation(userEmail: string, userName: string, requestId: string): Promise<boolean> {
    const template = this.getDeletionConfirmationTemplate(userName, requestId)

    return this.sendEmail(userEmail, template)
  }

  async sendDeletionCompletedNotification(userEmail: string, userName: string, requestId: string): Promise<boolean> {
    const template = this.getDeletionCompletedTemplate(userName, requestId)

    return this.sendEmail(userEmail, template)
  }

  async sendAdminDeletionNotification(adminEmail: string, userEmail: string, requestId: string): Promise<boolean> {
    const template = this.getAdminNotificationTemplate(userEmail, requestId)

    return this.sendEmail(adminEmail, template)
  }

  private async sendEmail(to: string, template: EmailTemplate): Promise<boolean> {
    try {
      // Check if we should actually send emails or just log them
      const shouldSendEmail = process.env.NODE_ENV === 'production' || this.config.forceEmailSending || process.env.FORCE_EMAIL_SENDING === 'true'

      if (!shouldSendEmail) {
        return true
      }

      // Send actual email in production
      const mailOptions = {
        from: this.config.from,
        to,
        subject: template.subject,
        text: template.text,
        html: template.html,
        // Gmail-specific options
        headers: {
          'X-Mailer': 'BuscaFarma Email Service',
          'X-Priority': '3' // Normal priority
        }
      }

      const _result = await this.transporter.sendMail(mailOptions)
      return true
    } catch (_error) {
      return false
    }
  }

  private getDeletionConfirmationTemplate(userName: string, requestId: string): EmailTemplate {
    const subject = 'BuscaFarma - Solicitud de Eliminación de Cuenta Recibida'

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>${subject}</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #2c3e50; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
          .request-id { background: #ecf0f1; padding: 10px; border-radius: 5px; font-family: monospace; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>BuscaFarma</h1>
            <h2>Solicitud de Eliminación de Cuenta</h2>
          </div>
          <div class="content">
            <p>Hola ${userName},</p>
            <p>Hemos recibido tu solicitud para eliminar tu cuenta de BuscaFarma y todos los datos asociados.</p>
            <p><strong>ID de Solicitud:</strong></p>
            <div class="request-id">${requestId}</div>
            <p><strong>¿Qué sucederá a continuación?</strong></p>
            <ul>
              <li>Procesaremos tu solicitud dentro de los próximos 30 días</li>
              <li>Eliminaremos permanentemente tu información personal, datos de salud, favoritos y preferencias</li>
              <li>Algunos datos pueden retenerse por razones legales (registros de auditoría)</li>
              <li>Te notificaremos cuando el proceso esté completo</li>
            </ul>
            <p>Si no solicitaste esta eliminación o tienes preguntas, contacta nuestro soporte inmediatamente.</p>
          </div>
          <div class="footer">
            <p>BuscaFarma - Encuentra tu farmacia más cercana</p>
            <p>Este es un mensaje automático, por favor no respondas a este correo.</p>
          </div>
        </div>
      </body>
      </html>
    `

    const text = `
BuscaFarma - Solicitud de Eliminación de Cuenta

Hola ${userName},

Hemos recibido tu solicitud para eliminar tu cuenta de BuscaFarma y todos los datos asociados.

ID de Solicitud: ${requestId}

¿Qué sucederá a continuación?
- Procesaremos tu solicitud dentro de los próximos 30 días
- Eliminaremos permanentemente tu información personal, datos de salud, favoritos y preferencias
- Algunos datos pueden retenerse por razones legales (registros de auditoría)
- Te notificaremos cuando el proceso esté completo

Si no solicitaste esta eliminación o tienes preguntas, contacta nuestro soporte inmediatamente.

BuscaFarma - Encuentra tu farmacia más cercana
Este es un mensaje automático, por favor no respondas a este correo.
    `

    return { subject, html, text }
  }

  private getDeletionCompletedTemplate(userName: string, requestId: string): EmailTemplate {
    const subject = 'BuscaFarma - Eliminación de Cuenta Completada'

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>${subject}</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #27ae60; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
          .request-id { background: #ecf0f1; padding: 10px; border-radius: 5px; font-family: monospace; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>BuscaFarma</h1>
            <h2>Eliminación de Cuenta Completada</h2>
          </div>
          <div class="content">
            <p>Hola ${userName},</p>
            <p>Tu solicitud de eliminación de cuenta ha sido procesada exitosamente.</p>
            <p><strong>ID de Solicitud:</strong></p>
            <div class="request-id">${requestId}</div>
            <p><strong>Datos eliminados:</strong></p>
            <ul>
              <li>Información personal (nombre, email)</li>
              <li>Datos de ubicación</li>
              <li>Datos de salud y preferencias</li>
              <li>Medicamentos favoritos</li>
              <li>Tokens de autenticación</li>
            </ul>
            <p>Gracias por haber usado BuscaFarma. Si decides volver en el futuro, serás bienvenido/a a crear una nueva cuenta.</p>
          </div>
          <div class="footer">
            <p>BuscaFarma - Encuentra tu farmacia más cercana</p>
          </div>
        </div>
      </body>
      </html>
    `

    const text = `
BuscaFarma - Eliminación de Cuenta Completada

Hola ${userName},

Tu solicitud de eliminación de cuenta ha sido procesada exitosamente.

ID de Solicitud: ${requestId}

Datos eliminados:
- Información personal (nombre, email)
- Datos de ubicación
- Datos de salud y preferencias
- Medicamentos favoritos
- Tokens de autenticación

Gracias por haber usado BuscaFarma. Si decides volver en el futuro, serás bienvenido/a a crear una nueva cuenta.

BuscaFarma - Encuentra tu farmacia más cercana
    `

    return { subject, html, text }
  }

  private getAdminNotificationTemplate(userEmail: string, requestId: string): EmailTemplate {
    const subject = `BuscaFarma Admin - Nueva Solicitud de Eliminación: ${requestId}`

    const html = `
      <h2>Nueva Solicitud de Eliminación de Cuenta</h2>
      <p><strong>Usuario:</strong> ${userEmail}</p>
      <p><strong>ID de Solicitud:</strong> ${requestId}</p>
      <p><strong>Fecha:</strong> ${new Date().toLocaleString('es-ES')}</p>
      <p>Revisa el panel de administración para procesar esta solicitud.</p>
    `

    const text = `
Nueva Solicitud de Eliminación de Cuenta

Usuario: ${userEmail}
ID de Solicitud: ${requestId}
Fecha: ${new Date().toLocaleString('es-ES')}

Revisa el panel de administración para procesar esta solicitud.
    `

    return { subject, html, text }
  }
}
