import { Elysia, t } from 'elysia'

// Import services and dependencies using barrel export to avoid deep relative imports
import {
  AddMedicationFavorite,
  JWT,
  ListMedicationFavorites,
  LoginController,
  LoginUser,
  MedicationFavoriteController,
  SearchMedicationFavorites,
  addMedicationFavoriteDTO,
  firebaseLoginDTO,
  listMedicationFavoritesDTO,
  refreshTokenDTO,
  removeMedicationFavoriteDTO,
  searchMedicationFavoritesDTO
} from '../../testUtils.js'

// Legal functionality implemented inline to avoid additional deep imports

// User DTOs
const updatePreferencesDTO = {
  body: t.Object({
    language: t.String(),
    theme: t.Union([t.Literal('light'), t.Literal('dark'), t.Literal('system')]),
    notifications: t.<PERSON>(),
    defaultLocation: t.Optional(
      t.Object({
        departmentCode: t.String(),
        provinceCode: t.String(),
        districtCode: t.String()
      })
    )
  })
}

const updateHealthDataDTO = {
  body: t.Object({
    allergies: t.Array(t.String()),
    conditions: t.Array(t.String()),
    medications: t.Array(
      t.Object({
        name: t.String(),
        dosage: t.String(),
        frequency: t.String()
      })
    )
  })
}

const updateLocationDTO = {
  body: t.Object({
    latitude: t.Number({ minimum: -90, maximum: 90 }),
    longitude: t.Number({ minimum: -180, maximum: 180 })
  })
}

const updateProfilePictureDTO = {
  body: t.Object({
    profilePictureUrl: t.Union([t.String({ format: 'uri' }), t.Null()])
  })
}

// Test repository implementations that accept Prisma client
class TestUserRepository {
  constructor(private readonly db: any) {}

  private mapToUser(dbUser: Record<string, unknown>) {
    const user = {
      id: dbUser.id,
      name: dbUser.name,
      email: dbUser.email,
      firebaseToken: dbUser.firebase_token,
      currentLocation: dbUser.current_location ? JSON.parse(dbUser.current_location as string) : undefined,
      preferences: dbUser.preferences
        ? JSON.parse(dbUser.preferences as string)
        : {
            language: 'es',
            theme: 'light',
            notifications: true
          },
      healthData: dbUser.health_data
        ? JSON.parse(dbUser.health_data as string)
        : {
            allergies: [],
            conditions: [],
            medications: []
          },
      profilePictureUrl: dbUser.profile_picture_url,
      refreshToken: dbUser.refreshToken,
      accessToken: undefined as string | undefined,
      setTokens(accessToken: string, refreshToken: string) {
        this.accessToken = accessToken
        this.refreshToken = refreshToken
      },
      getRefreshToken() {
        return this.refreshToken
      },
      toJSON(): Record<string, unknown> {
        return {
          id: this.id,
          name: this.name,
          email: this.email,
          profilePictureUrl: this.profilePictureUrl,
          accessToken: this.accessToken,
          refreshToken: this.refreshToken,
          preferences: this.preferences,
          healthData: this.healthData,
          currentLocation: this.currentLocation
        }
      }
    }
    return user
  }

  async findByEmail(email: string) {
    const user = await this.db.user.findUnique({
      where: { email }
    })
    return user ? this.mapToUser(user) : null
  }

  async create(id: string, name: string, email: string, profilePictureUrl: string, firebaseToken: string) {
    const defaultPreferences = {
      language: 'es',
      theme: 'light',
      notifications: true
    }

    const defaultHealthData = {
      allergies: [],
      conditions: [],
      medications: []
    }

    const user = await this.db.user.create({
      data: {
        id,
        name,
        email,
        firebase_token: firebaseToken,
        preferences: JSON.stringify(defaultPreferences),
        health_data: JSON.stringify(defaultHealthData),
        profile_picture_url: profilePictureUrl
      }
    })

    return this.mapToUser(user)
  }

  async findById(id: string) {
    const user = await this.db.user.findUnique({
      where: { id }
    })
    return user ? this.mapToUser(user) : null
  }

  async updatePreferences(id: string, preferences: Record<string, unknown>) {
    const user = await this.db.user.findUnique({ where: { id } })
    if (!user) throw new Error('User not found')

    const currentPreferences = user.preferences ? JSON.parse(user.preferences as string) : {}
    const updatedPreferences = { ...currentPreferences, ...preferences }

    const updatedUser = await this.db.user.update({
      where: { id },
      data: {
        preferences: JSON.stringify(updatedPreferences)
      }
    })

    return this.mapToUser(updatedUser)
  }

  async updateHealthData(id: string, healthData: Record<string, unknown>) {
    const user = await this.db.user.findUnique({ where: { id } })
    if (!user) throw new Error('User not found')

    const currentHealthData = user.health_data ? JSON.parse(user.health_data as string) : {}
    const updatedHealthData = { ...currentHealthData, ...healthData }

    const updatedUser = await this.db.user.update({
      where: { id },
      data: {
        health_data: JSON.stringify(updatedHealthData)
      }
    })

    return this.mapToUser(updatedUser)
  }

  async updateLocation(id: string, location: Record<string, unknown>) {
    const updatedUser = await this.db.user.update({
      where: { id },
      data: {
        current_location: JSON.stringify(location)
      }
    })

    return this.mapToUser(updatedUser)
  }

  async updateProfilePicture(id: string, url: string | null) {
    const updatedUser = await this.db.user.update({
      where: { id },
      data: {
        profile_picture_url: url
      }
    })

    return this.mapToUser(updatedUser)
  }

  async updateFirebaseToken(id: string, token: string) {
    const updatedUser = await this.db.user.update({
      where: { id },
      data: {
        firebase_token: token
      }
    })

    return this.mapToUser(updatedUser)
  }

  async updateRefreshToken(id: string, token: string) {
    const updatedUser = await this.db.user.update({
      where: { id },
      data: {
        refreshToken: token
      }
    })

    return this.mapToUser(updatedUser)
  }

  async findOrCreateFromFirebase(firebaseId: string, email: string, name: string, profilePictureUrl: string, firebaseToken: string) {
    let user = await this.findById(firebaseId)
    let isNewUser = false

    if (!user) {
      user = await this.create(firebaseId, name, email, profilePictureUrl, firebaseToken)
      isNewUser = true
    } else {
      user = await this.updateFirebaseToken(firebaseId, firebaseToken)
    }

    return { user, isNewUser }
  }
}

class TestMedicationFavoriteRepository {
  constructor(private readonly db: any) {}

  private mapToMedicationFavorite(dbFavorite: Record<string, unknown>) {
    return {
      id: dbFavorite.id,
      userId: dbFavorite.user_id,
      codEstab: dbFavorite.cod_estab,
      codProdE: dbFavorite.cod_prod_e,
      medicationData: typeof dbFavorite.medication_data === 'string' ? JSON.parse(dbFavorite.medication_data) : dbFavorite.medication_data,
      createdAt: dbFavorite.created_at,
      updatedAt: dbFavorite.updated_at,
      toPublicJSON() {
        return {
          id: this.id,
          userId: this.userId,
          codEstab: this.codEstab,
          codProdE: this.codProdE,
          medicationData: this.medicationData,
          createdAt: this.createdAt,
          updatedAt: this.updatedAt
        }
      }
    }
  }

  async create(userId: string, medicationData: Record<string, unknown>) {
    const favorite = await this.db.medication_favorite.create({
      data: {
        user_id: userId,
        cod_estab: medicationData.codEstab,
        cod_prod_e: medicationData.codProdE,
        medication_data: JSON.stringify(medicationData)
      }
    })

    return this.mapToMedicationFavorite(favorite)
  }

  async findByUserAndMedication(userId: string, codEstab: string, codProdE: number) {
    const favorite = await this.db.medication_favorite.findFirst({
      where: {
        user_id: userId,
        cod_estab: codEstab,
        cod_prod_e: codProdE
      }
    })

    if (!favorite) return null
    return this.mapToMedicationFavorite(favorite)
  }

  async findByIdAndUser(id: string, userId: string) {
    const favorite = await this.db.medication_favorite.findFirst({
      where: {
        id,
        user_id: userId
      }
    })

    if (!favorite) return null
    return this.mapToMedicationFavorite(favorite)
  }

  async findByUser(userId: string, options: { page: number; limit: number }) {
    const skip = (options.page - 1) * options.limit

    const [favorites, total] = await Promise.all([
      this.db.medication_favorite.findMany({
        where: { user_id: userId },
        orderBy: { created_at: 'desc' },
        skip,
        take: options.limit
      }),
      this.db.medication_favorite.count({
        where: { user_id: userId }
      })
    ])

    const totalPages = Math.ceil(total / options.limit)

    return {
      data: favorites.map((f: any) => this.mapToMedicationFavorite(f)),
      pagination: {
        page: options.page,
        limit: options.limit,
        total,
        totalPages,
        hasNext: options.page < totalPages,
        hasPrev: options.page > 1
      }
    }
  }

  async searchByUser(userId: string, options: { page: number; limit: number; query: string }) {
    const skip = (options.page - 1) * options.limit
    const searchQuery = options.query.toLowerCase()

    // Get all user favorites first, then filter in memory for search
    const allUserFavorites = await this.db.medication_favorite.findMany({
      where: { user_id: userId },
      orderBy: { created_at: 'desc' }
    })

    // Filter favorites based on search query
    const filteredFavorites = allUserFavorites.filter((favorite: any) => {
      const medicationData = typeof favorite.medication_data === 'string' ? JSON.parse(favorite.medication_data) : favorite.medication_data
      if (!medicationData) return false

      const searchFields = [
        medicationData.nombreProducto,
        medicationData.nombreComercial,
        medicationData.nombreSustancia,
        medicationData.nombreLaboratorio,
        medicationData.concent
      ]

      return searchFields.some((field) => field && typeof field === 'string' && field.toLowerCase().includes(searchQuery))
    })

    // Apply pagination to filtered results
    const total = filteredFavorites.length
    const paginatedFavorites = filteredFavorites.slice(skip, skip + options.limit)
    const totalPages = Math.ceil(total / options.limit)

    return {
      data: paginatedFavorites.map((f: any) => this.mapToMedicationFavorite(f)),
      pagination: {
        page: options.page,
        limit: options.limit,
        total,
        totalPages,
        hasNext: options.page < totalPages,
        hasPrev: options.page > 1
      }
    }
  }

  async removeByIdAndUser(id: string, userId: string) {
    try {
      await this.db.medication_favorite.deleteMany({
        where: {
          id,
          user_id: userId
        }
      })
      return true
    } catch (_error) {
      return false
    }
  }

  async removeByUserAndMedication(userId: string, codEstab: string, codProdE: number) {
    try {
      await this.db.medication_favorite.deleteMany({
        where: {
          user_id: userId,
          cod_estab: codEstab,
          cod_prod_e: codProdE
        }
      })
      return true
    } catch (_error) {
      return false
    }
  }
}

// Test version of RemoveMedicationFavorite that uses CUID validation
class TestRemoveMedicationFavorite {
  constructor(
    private readonly medicationFavoriteRepository: TestMedicationFavoriteRepository,
    private readonly jwtService: JWT
  ) {}

  // Remove by favorite ID
  async executeById(token: string, favoriteId: string): Promise<boolean> {
    // Verify and extract user ID from token
    const payload = this.jwtService.verify(token)
    const userId = payload.data

    if (!userId) {
      throw new Error('Invalid token: user ID not found')
    }

    // Validate favorite ID
    if (!favoriteId || favoriteId.trim() === '') {
      throw new Error('Favorite ID is required')
    }

    // Validate CUID format (for SQLite tests)
    if (!this.isValidCUID(favoriteId)) {
      throw new Error('Invalid favorite ID format')
    }

    // Check if favorite exists and belongs to user
    const favorite = await this.medicationFavoriteRepository.findByIdAndUser(favoriteId, userId)

    if (!favorite) {
      throw new Error('Favorite not found or does not belong to user')
    }

    // Remove the favorite
    const removed = await this.medicationFavoriteRepository.removeByIdAndUser(favoriteId, userId)

    if (!removed) {
      throw new Error('Failed to remove favorite')
    }

    return true
  }

  // Remove by medication identifiers
  async executeByMedication(token: string, codEstab: string, codProdE: number): Promise<boolean> {
    // Verify and extract user ID from token
    const payload = this.jwtService.verify(token)
    const userId = payload.data

    if (!userId) {
      throw new Error('Invalid token: user ID not found')
    }

    // Validate medication identifiers
    if (!codEstab || codEstab.trim() === '') {
      throw new Error('Establishment code is required')
    }

    if (!codProdE || codProdE <= 0) {
      throw new Error('Product code is required and must be positive')
    }

    // Check if favorite exists
    const favorite = await this.medicationFavoriteRepository.findByUserAndMedication(userId, codEstab, codProdE)

    if (!favorite) {
      throw new Error('Medication is not in favorites')
    }

    // Remove the favorite
    const removed = await this.medicationFavoriteRepository.removeByUserAndMedication(userId, codEstab, codProdE)

    if (!removed) {
      throw new Error('Failed to remove favorite')
    }

    return true
  }

  // Validate CUID format (for SQLite tests)
  private isValidCUID(id: string): boolean {
    // CUID format: starts with 'c' followed by 24 characters
    const cuidRegex = /^c[a-z0-9]{24}$/
    return cuidRegex.test(id)
  }
}

export const createTestDependencies = (testPrismaClient: any) => {
  // Shared services
  const jwtService = new JWT()

  // User dependencies
  const userRepository = new TestUserRepository(testPrismaClient)
  const loginUser = new LoginUser(userRepository as unknown as any, jwtService)
  const loginController = new LoginController(loginUser)

  // Medication favorites dependencies
  const medicationFavoriteRepository = new TestMedicationFavoriteRepository(testPrismaClient)
  const addMedicationFavorite = new AddMedicationFavorite(medicationFavoriteRepository as unknown as any, jwtService)
  const removeMedicationFavorite = new TestRemoveMedicationFavorite(medicationFavoriteRepository, jwtService)
  const listMedicationFavorites = new ListMedicationFavorites(medicationFavoriteRepository as unknown as any, jwtService)
  const searchMedicationFavorites = new SearchMedicationFavorites(medicationFavoriteRepository as unknown as any, jwtService)
  const medicationFavoriteController = new MedicationFavoriteController(
    addMedicationFavorite,
    removeMedicationFavorite as unknown as any,
    listMedicationFavorites,
    searchMedicationFavorites
  )

  // Legal functionality - inline implementation to avoid deep imports
  const testLegalController = {
    async getPrivacyPolicy() {
      const htmlContent = `<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Política de Privacidad - BuscaFarma</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; max-width: 800px; margin: 0 auto; padding: 20px; background-color: #f9f9f9; }
        .container { background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px; }
        h2 { color: #34495e; margin-top: 30px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Política de Privacidad - BuscaFarma</h1>
        <h2>1. Información que recopilamos</h2>
        <p>En BuscaFarma, recopilamos información para brindarte un mejor servicio.</p>
        <h2>2. Contacto</h2>
        <p>Email: <EMAIL></p>
    </div>
</body>
</html>`

      return {
        status: 200,
        headers: {
          'Content-Type': 'text/html; charset=utf-8',
          'Cache-Control': 'public, max-age=3600'
        },
        body: htmlContent
      }
    },

    async getDataDeletionForm() {
      const htmlContent = `<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Solicitud de Eliminación de Cuenta - BuscaFarma</title>
</head>
<body>
    <h1>🏥 BuscaFarma</h1>
    <h2>Solicitud de Eliminación de Cuenta</h2>
    <form id="deletionRequestForm">
        <input type="email" name="email" required>
        <input type="checkbox" name="confirmDeletion" required>
        <input type="checkbox" name="confirmIdentity" required>
        <button type="submit">Enviar Solicitud</button>
    </form>
</body>
</html>`

      return {
        status: 200,
        headers: {
          'Content-Type': 'text/html; charset=utf-8',
          'Cache-Control': 'public, max-age=3600'
        },
        body: htmlContent
      }
    },

    async getLegalDocument(documentType: string) {
      if (documentType === 'privacy-policy') {
        return this.getPrivacyPolicy()
      }

      const notFoundHtml = `<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <title>Documento no encontrado - BuscaFarma</title>
</head>
<body>
    <h1>Documento no encontrado</h1>
    <p>El documento "${documentType}" no está disponible.</p>
</body>
</html>`

      return {
        status: 404,
        headers: {
          'Content-Type': 'text/html; charset=utf-8'
        },
        body: notFoundHtml
      }
    }
  }

  // Data deletion functionality
  const testDataDeletionController = {
    async createDeletionRequest(requestData: any) {
      const { body } = requestData

      // Validate required fields
      if (!body.email || body.email.trim() === '') {
        return {
          status: 400,
          data: { message: 'Email is required' }
        }
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(body.email)) {
        return {
          status: 400,
          data: { message: 'Invalid email format' }
        }
      }

      if (!body.confirmDeletion) {
        return {
          status: 400,
          data: { message: 'You must confirm that you understand the deletion consequences' }
        }
      }

      if (!body.confirmIdentity) {
        return {
          status: 400,
          data: { message: 'You must confirm that you are the account owner' }
        }
      }

      // Check for duplicate requests
      const existingRequest = await testPrismaClient.data_deletion_request.findFirst({
        where: {
          email: body.email,
          status: 'pending'
        }
      })

      if (existingRequest) {
        return {
          status: 409,
          data: { message: 'A pending deletion request already exists for this email' }
        }
      }

      // Create deletion request
      const dataRetentionInfo = [
        {
          dataType: 'Personal Information (name, email)',
          willBeDeleted: true,
          retentionPeriod: 'Immediate',
          reason: 'User account data'
        },
        {
          dataType: 'Location Data',
          willBeDeleted: true,
          retentionPeriod: 'Immediate',
          reason: 'User preference data'
        },
        {
          dataType: 'Health Data & Preferences',
          willBeDeleted: true,
          retentionPeriod: 'Immediate',
          reason: 'User health information'
        },
        {
          dataType: 'Medication Favorites',
          willBeDeleted: true,
          retentionPeriod: 'Immediate',
          reason: 'User preference data'
        },
        {
          dataType: 'Authentication Tokens',
          willBeDeleted: true,
          retentionPeriod: 'Immediate',
          reason: 'Security tokens'
        },
        {
          dataType: 'Audit Logs',
          willBeDeleted: false,
          retentionPeriod: '7 years',
          reason: 'Legal compliance and fraud prevention'
        },
        {
          dataType: 'Deletion Request Record',
          willBeDeleted: false,
          retentionPeriod: '7 years',
          reason: 'Compliance with data protection regulations'
        }
      ]

      const deletionRequest = await testPrismaClient.data_deletion_request.create({
        data: {
          email: body.email,
          status: 'pending',
          reason: body.reason || null,
          data_retention_info: JSON.stringify(dataRetentionInfo)
        }
      })

      return {
        status: 201,
        data: {
          success: true,
          requestId: deletionRequest.id,
          email: body.email,
          status: 'pending',
          estimatedCompletion: 'Within 30 days',
          dataRetentionInfo
        }
      }
    },

    async getRequestStatus(requestId: string) {
      const request = await testPrismaClient.data_deletion_request.findUnique({
        where: { id: requestId }
      })

      if (!request) {
        return {
          status: 404,
          data: { message: 'Deletion request not found' }
        }
      }

      return {
        status: 200,
        data: {
          requestId: request.id,
          email: request.email,
          status: request.status,
          requestedAt: request.requested_at,
          estimatedCompletion: 'Within 30 days'
        }
      }
    }
  }

  // Create routers
  const userRouter = new Elysia({ prefix: '/users' })
    .derive(({ headers }: any) => {
      try {
        const auth = headers?.['authorization'] ?? headers?.['Authorization']
        if (auth && typeof auth === 'string' && auth.startsWith('Bearer ')) {
          return { token: auth.slice(7) }
        }
        return { token: null }
      } catch (_error) {
        return { token: null }
      }
    })
    .post(
      '/firebase/login',
      async ({ body, set }: any) => {
        const result = await loginController.loginWithFirebase({ body } as any)
        if (set) set.status = result.status
        return result
      },
      firebaseLoginDTO
    )
    .post(
      '/refresh-token',
      async ({ body, set }: any) => {
        const result = await loginController.refreshToken({ body } as any)
        if (set) set.status = result.status
        return result
      },
      refreshTokenDTO
    )
    .put(
      '/preferences',
      async ({ body, token, set }: any) => {
        const result = await loginController.updatePreferences({ body, token } as any)
        if (set) set.status = result.status
        return result
      },
      updatePreferencesDTO
    )
    .put(
      '/health-data',
      async ({ body, token, set }: any) => {
        const result = await loginController.updateHealthData({ body, token } as any)
        if (set) set.status = result.status
        return result
      },
      updateHealthDataDTO
    )
    .put(
      '/location',
      async ({ body, token, set }: any) => {
        const result = await loginController.updateLocation({ body, token } as any)
        if (set) set.status = result.status
        return result
      },
      updateLocationDTO
    )
    .put(
      '/profile-picture',
      async ({ body, token, set }: any) => {
        const result = await loginController.updateProfilePicture({ body, token } as any)
        if (set) set.status = result.status
        return result
      },
      updateProfilePictureDTO
    )
    .get('/profile', async ({ token, set }: any) => {
      const result = await loginController.getProfile({ token } as any)
      if (set) set.status = result.status
      return result
    })

  const favoritesRouter = new Elysia({ prefix: '/favorites' })
    .derive(({ headers }: any) => {
      try {
        const auth = headers?.['authorization'] ?? headers?.['Authorization']
        if (auth && typeof auth === 'string' && auth.startsWith('Bearer ')) {
          return { token: auth.slice(7) }
        }
        return { token: null }
      } catch (_error) {
        return { token: null }
      }
    })
    .post(
      '/medications',
      async ({ body, token, set }: any) => {
        const result = await medicationFavoriteController.addFavorite({ body, token } as any)
        if (set) set.status = result.status
        return result
      },
      addMedicationFavoriteDTO
    )
    .delete(
      '/medications',
      async ({ body, token, set }: any) => {
        const result = await medicationFavoriteController.removeFavorite({ body, token } as any)
        if (set) set.status = result.status
        return result
      },
      removeMedicationFavoriteDTO
    )
    .delete(
      '/medications/:id',
      async ({ params, token, set }: any) => {
        const result = await medicationFavoriteController.removeFavoriteById({ params, token } as any)
        if (set) set.status = result.status
        return result
      },
      {
        params: t.Object({
          id: t.String()
        })
      }
    )
    .get(
      '/medications',
      async ({ query, token, set }: any) => {
        const result = await medicationFavoriteController.listFavorites({ query, token } as any)
        if (set) set.status = result.status
        return result
      },
      listMedicationFavoritesDTO
    )
    .get(
      '/medications/search',
      async ({ query, token, set }: any) => {
        const result = await medicationFavoriteController.searchFavorites({ query, token } as any)
        if (set) set.status = result.status
        return result
      },
      searchMedicationFavoritesDTO
    )

  // Legal router (no authentication required)
  const legalRouter = new Elysia()
    .get('/privacy-policy.html', async ({ set }: any) => {
      const result = await testLegalController.getPrivacyPolicy()

      // Set response headers
      if (result.headers) {
        Object.entries(result.headers).forEach(([key, value]) => {
          set.headers[key] = value
        })
      }

      // Set status code
      set.status = result.status

      return result.body
    })
    .get('/data-deletion.html', async ({ set }: any) => {
      const result = await testLegalController.getDataDeletionForm()

      // Set response headers
      if (result.headers) {
        Object.entries(result.headers).forEach(([key, value]) => {
          set.headers[key] = value
        })
      }

      // Set status code
      set.status = result.status

      return result.body
    })
    .post('/data-deletion/request', async ({ body, set }: any) => {
      const result = await testDataDeletionController.createDeletionRequest({ body })

      // Set status code
      set.status = result.status

      return result.data
    }, {
      body: t.Object({
        email: t.String(),
        reason: t.Optional(t.String()),
        confirmDeletion: t.Boolean(),
        confirmIdentity: t.Boolean()
      })
    })
    .get('/data-deletion/status/:requestId', async ({ params, set }: any) => {
      const result = await testDataDeletionController.getRequestStatus(params.requestId)

      // Set status code
      set.status = result.status

      return result.data
    })
    .get('/legal/:documentType', async ({ params, set }: any) => {
      const result = await testLegalController.getLegalDocument(params.documentType)

      // Set response headers
      if (result.headers) {
        Object.entries(result.headers).forEach(([key, value]) => {
          set.headers[key] = value
        })
      }

      // Set status code
      set.status = result.status

      return result.body
    })

  return { userRouter, favoritesRouter, legalRouter }
}
