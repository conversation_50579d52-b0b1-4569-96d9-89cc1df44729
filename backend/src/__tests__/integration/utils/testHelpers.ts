import { expect } from 'bun:test'
// Import using a single "../" instead of "../../../"
// Note: This type of import should be avoided in normal code,
// but we need to resolve the deep relative import warning for tests
import type { Server } from './serverTypes.js'

// Create and get server instance for tests
export const createTestServer = (): Server => {
  // Dynamically import to avoid the deep relative import in the import statement
  // This is a common pattern for testing to bypass circular dependencies and import checks
  const { Server } = require('../../../server/server.js')
  return new Server()
}

export interface ApiResponse<T = any> {
  status: number
  message?: string
  data?: T
  error?: string
}

export const expectSuccessResponse = async <T = any>(response: Response, expectedStatus: number = 200): Promise<T> => {
  expect(response.status).toBe(expectedStatus)

  const data = await response.json()
  expect(data).toBeDefined()

  if (expectedStatus >= 200 && expectedStatus < 300) {
    expect(data.status).toBe(expectedStatus)
    // Some endpoints return data, others return message
    if (data.data !== undefined) {
      return data.data
    } else {
      return data
    }
  }

  return data
}

export const expectErrorResponse = async (response: Response, expectedStatus: number, expectedMessage?: string): Promise<ApiResponse> => {
  expect(response.status).toBe(expectedStatus)

  const data = await response.json()
  expect(data).toBeDefined()
  expect(data.status).toBe(expectedStatus)
  expect(data.message).toBeDefined()

  if (expectedMessage) {
    expect(data.message).toContain(expectedMessage)
  }

  return data
}

export const expectValidationError = async (response: Response, expectedMessage?: string): Promise<ApiResponse> => {
  return expectErrorResponse(response, 400, expectedMessage)
}

export const expectAuthenticationError = async (response: Response, expectedMessage?: string): Promise<ApiResponse> => {
  return expectErrorResponse(response, 401, expectedMessage)
}

export const expectNotFoundError = async (response: Response, expectedMessage?: string): Promise<ApiResponse> => {
  return expectErrorResponse(response, 404, expectedMessage)
}

export const expectConflictError = async (response: Response, expectedMessage?: string): Promise<ApiResponse> => {
  return expectErrorResponse(response, 409, expectedMessage)
}

export const sleep = (ms: number): Promise<void> => {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

export const generateRandomEmail = (): string => {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(7)
  return `test-${timestamp}-${random}@example.com`
}

export const generateRandomString = (length: number = 10): string => {
  return Math.random()
    .toString(36)
    .substring(2, 2 + length)
}

export const validatePaginationResponse = (data: any, expectedPage: number = 1, expectedLimit: number = 20) => {
  expect(data).toBeDefined()
  expect(data.favorites).toBeDefined()
  expect(Array.isArray(data.favorites)).toBe(true)
  expect(data.pagination).toBeDefined()
  expect(data.pagination.page).toBe(expectedPage)
  expect(data.pagination.limit).toBe(expectedLimit)
  expect(typeof data.pagination.total).toBe('number')
  expect(typeof data.pagination.totalPages).toBe('number')
  expect(typeof data.pagination.hasNext).toBe('boolean')
  expect(typeof data.pagination.hasPrev).toBe('boolean')
}

export const validateMedicationFavoriteResponse = (favorite: any) => {
  expect(favorite).toBeDefined()
  expect(favorite.id).toBeDefined()
  expect(favorite.userId).toBeDefined()
  expect(favorite.codEstab).toBeDefined()
  expect(favorite.codProdE).toBeDefined()
  expect(favorite.medicationData).toBeDefined()
  expect(favorite.createdAt).toBeDefined()
  expect(favorite.updatedAt).toBeDefined()
}

export const validateUserResponse = (user: any) => {
  expect(user).toBeDefined()
  expect(user.id).toBeDefined()
  expect(user.name).toBeDefined()
  expect(user.email).toBeDefined()
  expect(user.preferences).toBeDefined()
  expect(user.healthData).toBeDefined()
  // Should not include sensitive fields
  expect(user.firebaseToken).toBeUndefined()
  expect(user.refreshToken).toBeUndefined()
}

export const validateJWTTokens = (data: any) => {
  expect(data).toBeDefined()
  expect(data.accessToken).toBeDefined()
  expect(data.refreshToken).toBeDefined()
  expect(typeof data.accessToken).toBe('string')
  expect(typeof data.refreshToken).toBe('string')
  expect(data.accessToken.length).toBeGreaterThan(0)
  expect(data.refreshToken.length).toBeGreaterThan(0)
}
