import { afterAll, afterEach, beforeAll, beforeEach, describe, expect, it } from 'bun:test'
import { cleanupTestDatabase, resetTestDatabase, seedTestDatabase, setupTestDatabase } from '../setup/database.js'
import { makeRequest, startTestServer, stopTestServer } from '../setup/server.js'

describe('Legal API Integration Tests', () => {
  beforeAll(async () => {
    await setupTestDatabase()
    await startTestServer()
  })

  afterAll(async () => {
    await stopTestServer()
    await cleanupTestDatabase()
  })

  beforeEach(async () => {
    await resetTestDatabase()
    await seedTestDatabase()
  })

  afterEach(async () => {
    // Cleanup after each test if needed
  })

  describe('GET /privacy-policy.html', () => {
    it('should return privacy policy HTML without authentication', async () => {
      const response = await makeRequest('GET', '/privacy-policy.html')

      expect(response.status).toBe(200)
      expect(response.headers.get('content-type')).toContain('text/html')
      expect(response.headers.get('cache-control')).toBe('public, max-age=3600')

      const html = await response.text()
      expect(html).toContain('<!DOCTYPE html>')
      expect(html).toContain('Política de Privacidad - BuscaFarma')
      expect(html).toContain('Información que recopilamos')
      expect(html).toContain('BuscaFarma')
      expect(html).toContain('<EMAIL>')
    })

    it('should return HTML with proper structure and styling', async () => {
      const response = await makeRequest('GET', '/privacy-policy.html')

      const html = await response.text()
      expect(html).toContain('<html lang="es">')
      expect(html).toContain('<meta charset="UTF-8">')
      expect(html).toContain('<meta name="viewport"')
      expect(html).toContain('<style>')
      expect(html).toContain('font-family:')
    })

    it('should include all required privacy policy sections', async () => {
      const response = await makeRequest('GET', '/privacy-policy.html')

      const html = await response.text()

      // Check for main sections (simplified test version)
      expect(html).toContain('Información que recopilamos')
      expect(html).toContain('Contacto')
    })
  })

  describe('GET /legal/:documentType', () => {
    it('should return privacy policy when requested via generic endpoint', async () => {
      const response = await makeRequest('GET', '/legal/privacy-policy')

      expect(response.status).toBe(200)
      expect(response.headers.get('content-type')).toContain('text/html')

      const html = await response.text()
      expect(html).toContain('Política de Privacidad - BuscaFarma')
    })

    it('should return 404 HTML for unimplemented document types', async () => {
      const response = await makeRequest('GET', '/legal/terms-of-service')

      expect(response.status).toBe(404)
      expect(response.headers.get('content-type')).toContain('text/html')

      const html = await response.text()
      expect(html).toContain('<!DOCTYPE html>')
      expect(html).toContain('Documento no encontrado')
      expect(html).toContain('terms-of-service')
    })

    it('should return 404 HTML for invalid document types', async () => {
      const response = await makeRequest('GET', '/legal/invalid-document')

      expect(response.status).toBe(404)
      expect(response.headers.get('content-type')).toContain('text/html')

      const html = await response.text()
      expect(html).toContain('Documento no encontrado')
      expect(html).toContain('invalid-document')
    })
  })

  describe('Public Access', () => {
    it('should be accessible without any authentication headers', async () => {
      // Make request without any headers
      const response = await fetch('http://localhost:3001/privacy-policy.html')

      expect(response.status).toBe(200)
      expect(response.headers.get('content-type')).toContain('text/html')
    })

    it('should be accessible with invalid authentication headers', async () => {
      const response = await makeRequest('GET', '/privacy-policy.html', {
        headers: {
          Authorization: 'Bearer invalid-token'
        }
      })

      expect(response.status).toBe(200)
      expect(response.headers.get('content-type')).toContain('text/html')
    })
  })

  describe('Caching Headers', () => {
    it('should include appropriate cache headers', async () => {
      const response = await makeRequest('GET', '/privacy-policy.html')

      expect(response.headers.get('cache-control')).toBe('public, max-age=3600')
    })
  })
})
