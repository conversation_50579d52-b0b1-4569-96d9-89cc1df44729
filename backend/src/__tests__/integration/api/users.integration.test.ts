import { afterAll, beforeAll, beforeEach, describe, expect, it } from 'bun:test'
import {
  generateTestAccessToken,
  generateTestRefreshToken,
  getTestAuthHeaders,
  getTestHealthData,
  getTestLocationData,
  getTestUserPreferences,
  testUsers
} from '../setup/auth.js'
import { cleanupTestDatabase, resetTestDatabase, seedTestDatabase, setupTestDatabase } from '../setup/database.js'
import { makeAuthenticatedRequest, makeRequest, startTestServer, stopTestServer } from '../setup/server.js'
import {
  createFirebaseLoginPayload,
  testHealthData,
  testHealthData2,
  testLocationData,
  testLocationData2,
  testUserPreferences,
  testUserPreferences2
} from '../utils/fixtures.js'
import {
  expectAuthenticationError,
  expectErrorResponse,
  expectSuccessResponse,
  expectValidationError,
  generateRandomEmail,
  validateJWTTokens,
  validateUserResponse
} from '../utils/testHelpers.js'

describe('Users API Integration Tests', () => {
  beforeAll(async () => {
    await setupTestDatabase()
    await startTestServer()
  })

  afterAll(async () => {
    await stopTestServer()
    await cleanupTestDatabase()
  })

  beforeEach(async () => {
    await resetTestDatabase()
    await seedTestDatabase()
  })

  describe('POST /users/firebase/login', () => {
    describe('Successful login scenarios', () => {
      it('should login existing user with valid Firebase credentials', async () => {
        const loginPayload = createFirebaseLoginPayload({
          firebaseId: testUsers.user1.id,
          email: testUsers.user1.email,
          name: testUsers.user1.name,
          firebaseToken: testUsers.user1.firebaseToken
        })

        const response = await makeRequest('POST', '/users/firebase/login', {
          body: loginPayload
        })

        const data = await expectSuccessResponse(response, 200)
        validateJWTTokens(data)
        expect(data.user).toBeDefined()
        validateUserResponse(data.user)
        expect(data.user.email).toBe(loginPayload.email)
      })

      it('should create new user and login with valid Firebase credentials', async () => {
        const loginPayload = createFirebaseLoginPayload({
          email: generateRandomEmail(),
          name: 'Brand New User'
        })

        const response = await makeRequest('POST', '/users/firebase/login', {
          body: loginPayload
        })

        const data = await expectSuccessResponse(response, 201)
        validateJWTTokens(data)
        expect(data.user).toBeDefined()
        validateUserResponse(data.user)
        expect(data.user.email).toBe(loginPayload.email)
        expect(data.user.name).toBe(loginPayload.name)
      })
    })

    describe('Validation error scenarios', () => {
      it('should reject login with missing firebaseId', async () => {
        const loginPayload = createFirebaseLoginPayload()
        delete loginPayload.firebaseId

        const response = await makeRequest('POST', '/users/firebase/login', {
          body: loginPayload
        })

        await expectValidationError(response)
      })

      it('should reject login with missing email', async () => {
        const loginPayload = createFirebaseLoginPayload()
        delete loginPayload.email

        const response = await makeRequest('POST', '/users/firebase/login', {
          body: loginPayload
        })

        await expectValidationError(response)
      })

      it('should reject login with invalid email format', async () => {
        const loginPayload = createFirebaseLoginPayload({
          email: 'invalid-email'
        })

        const response = await makeRequest('POST', '/users/firebase/login', {
          body: loginPayload
        })

        await expectValidationError(response)
      })

      it('should reject login with missing firebaseToken', async () => {
        const loginPayload = createFirebaseLoginPayload()
        delete loginPayload.firebaseToken

        const response = await makeRequest('POST', '/users/firebase/login', {
          body: loginPayload
        })

        await expectValidationError(response)
      })
    })
  })

  describe('POST /users/refresh-token', () => {
    describe('Successful refresh scenarios', () => {
      it('should refresh access token with valid refresh token', async () => {
        const refreshToken = generateTestRefreshToken(testUsers.user1.id)

        const response = await makeRequest('POST', '/users/refresh-token', {
          body: { refreshToken }
        })

        const data = await expectSuccessResponse(response, 200)
        expect(data.accessToken).toBeDefined()
        expect(typeof data.accessToken).toBe('string')
        expect(data.accessToken.length).toBeGreaterThan(0)
      })
    })

    describe('Error scenarios', () => {
      it('should reject refresh with missing refresh token', async () => {
        const response = await makeRequest('POST', '/users/refresh-token', {
          body: {}
        })

        await expectValidationError(response)
      })

      it('should reject refresh with invalid refresh token', async () => {
        const response = await makeRequest('POST', '/users/refresh-token', {
          body: { refreshToken: 'invalid-token' }
        })

        await expectAuthenticationError(response)
      })

      it('should reject refresh with expired refresh token', async () => {
        // Create an expired token (this would need JWT with past expiry)
        const expiredToken = 'expired.jwt.token'

        const response = await makeRequest('POST', '/users/refresh-token', {
          body: { refreshToken: expiredToken }
        })

        await expectAuthenticationError(response)
      })
    })
  })

  describe('PUT /users/preferences', () => {
    describe('Successful update scenarios', () => {
      it('should update user preferences with valid data', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)

        const response = await makeAuthenticatedRequest('PUT', '/users/preferences', token, {
          body: testUserPreferences
        })

        const data = await expectSuccessResponse(response, 200)
        expect(data.preferences).toEqual(testUserPreferences)
      })

      it('should update preferences with partial data', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)
        const partialPreferences = {
          language: 'en',
          theme: 'dark' as const,
          notifications: false
        }

        const response = await makeAuthenticatedRequest('PUT', '/users/preferences', token, {
          body: partialPreferences
        })

        const data = await expectSuccessResponse(response, 200)
        expect(data.preferences.language).toBe('en')
        expect(data.preferences.theme).toBe('dark')
        expect(data.preferences.notifications).toBe(false)
      })
    })

    describe('Authentication and validation errors', () => {
      it('should reject update without authentication token', async () => {
        const response = await makeRequest('PUT', '/users/preferences', {
          body: testUserPreferences
        })

        await expectAuthenticationError(response)
      })

      it('should reject update with invalid authentication token', async () => {
        const response = await makeAuthenticatedRequest('PUT', '/users/preferences', 'invalid-token', {
          body: testUserPreferences
        })

        await expectAuthenticationError(response)
      })

      it('should reject update with invalid theme value', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)
        const invalidPreferences = {
          ...testUserPreferences,
          theme: 'invalid-theme'
        }

        const response = await makeAuthenticatedRequest('PUT', '/users/preferences', token, {
          body: invalidPreferences
        })

        await expectValidationError(response)
      })
    })
  })

  describe('PUT /users/health-data', () => {
    describe('Successful update scenarios', () => {
      it('should update user health data with valid data', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)

        const response = await makeAuthenticatedRequest('PUT', '/users/health-data', token, {
          body: testHealthData
        })

        const data = await expectSuccessResponse(response, 200)
        expect(data.healthData).toEqual(testHealthData)
      })

      it('should update health data with empty arrays', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)
        const emptyHealthData = {
          allergies: [],
          conditions: [],
          medications: []
        }

        const response = await makeAuthenticatedRequest('PUT', '/users/health-data', token, {
          body: emptyHealthData
        })

        const data = await expectSuccessResponse(response, 200)
        expect(data.healthData).toEqual(emptyHealthData)
      })
    })

    describe('Authentication and validation errors', () => {
      it('should reject update without authentication token', async () => {
        const response = await makeRequest('PUT', '/users/health-data', {
          body: testHealthData
        })

        await expectAuthenticationError(response)
      })

      it('should reject update with invalid medication structure', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)
        const invalidHealthData = {
          allergies: ['penicillin'],
          conditions: ['diabetes'],
          medications: [
            {
              name: 'Metformin'
              // Missing dosage and frequency
            }
          ]
        }

        const response = await makeAuthenticatedRequest('PUT', '/users/health-data', token, {
          body: invalidHealthData
        })

        await expectValidationError(response)
      })
    })
  })

  describe('PUT /users/location', () => {
    describe('Successful update scenarios', () => {
      it('should update user location with valid coordinates', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)

        const response = await makeAuthenticatedRequest('PUT', '/users/location', token, {
          body: testLocationData
        })

        const data = await expectSuccessResponse(response, 200)
        expect(data.currentLocation).toEqual(testLocationData)
      })
    })

    describe('Authentication and validation errors', () => {
      it('should reject update without authentication token', async () => {
        const response = await makeRequest('PUT', '/users/location', {
          body: testLocationData
        })

        await expectAuthenticationError(response)
      })

      it('should reject update with invalid latitude', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)
        const invalidLocation = {
          latitude: 91, // Invalid latitude (> 90)
          longitude: -77.0428
        }

        const response = await makeAuthenticatedRequest('PUT', '/users/location', token, {
          body: invalidLocation
        })

        await expectValidationError(response)
      })

      it('should reject update with invalid longitude', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)
        const invalidLocation = {
          latitude: -12.0464,
          longitude: 181 // Invalid longitude (> 180)
        }

        const response = await makeAuthenticatedRequest('PUT', '/users/location', token, {
          body: invalidLocation
        })

        await expectValidationError(response)
      })
    })
  })

  describe('PUT /users/profile-picture', () => {
    describe('Successful update scenarios', () => {
      it('should update profile picture URL with valid URL', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)
        const profilePictureUrl = 'https://example.com/new-avatar.jpg'

        const response = await makeAuthenticatedRequest('PUT', '/users/profile-picture', token, {
          body: { profilePictureUrl }
        })

        const data = await expectSuccessResponse(response, 200)
        expect(data.profilePictureUrl).toBe(profilePictureUrl)
      })

      it('should remove profile picture with null URL', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)

        const response = await makeAuthenticatedRequest('PUT', '/users/profile-picture', token, {
          body: { profilePictureUrl: null }
        })

        const data = await expectSuccessResponse(response, 200)
        expect(data.profilePictureUrl).toBeNull()
      })
    })

    describe('Authentication and validation errors', () => {
      it('should reject update without authentication token', async () => {
        const response = await makeRequest('PUT', '/users/profile-picture', {
          body: { profilePictureUrl: 'https://example.com/avatar.jpg' }
        })

        await expectAuthenticationError(response)
      })

      it('should reject update with invalid URL format', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)

        const response = await makeAuthenticatedRequest('PUT', '/users/profile-picture', token, {
          body: { profilePictureUrl: 'invalid-url' }
        })

        await expectValidationError(response)
      })
    })
  })
})
