import type { IJWT } from '../../services/interfaces/IJWT.js'
import type { IUser } from '../domain/IUser.js'
import type { HealthData, Point, UserPreferences } from '../domain/User.js'

export class LoginUser {
  constructor(
    private readonly userRepository: IUser,
    private readonly jwt: IJWT
  ) {}

  private async getUserFromToken(token: string) {
    const { data: userId } = this.jwt.verify(token)
    const user = await this.userRepository.findById(userId)
    if (!user) throw new Error('User not found')
    return user
  }

  async loginWithFirebase(firebaseId: string, email: string, name: string, profilePictureUrl: string, firebaseToken: string) {
    const result = await this.userRepository.findOrCreateFromFirebase(firebaseId, email, name, profilePictureUrl, firebaseToken)
    const user = result.user
    const tokens = await this.jwt.sign(user.id)
    user.setTokens(tokens.accessToken, tokens.refreshToken)

    // Actualizar solo el refresh token en la base de datos
    await this.userRepository.updateRefreshToken(user.id, tokens.refreshToken)

    return { user, isNewUser: result.isNewUser }
  }

  async refreshAccessToken(refreshToken: string): Promise<string> {
    try {
      const { data: userId } = this.jwt.verifyRefresh(refreshToken)
      const user = await this.userRepository.findById(userId)
      if (!user) throw new Error('User not found')

      const storedRefreshToken = user.getRefreshToken()
      if (!storedRefreshToken || storedRefreshToken !== refreshToken) {
        throw new Error('Invalid refresh token')
      }

      return await this.jwt.refreshAccessToken(refreshToken)
    } catch (error) {
      if (error instanceof Error) {
        throw error
      }
      throw new Error('Invalid refresh token')
    }
  }

  async updatePreferences(token: string, preferences: Partial<UserPreferences>) {
    const user = await this.getUserFromToken(token)
    return await this.userRepository.updatePreferences(user.id, preferences)
  }

  async updateHealthData(token: string, healthData: Partial<HealthData>) {
    const user = await this.getUserFromToken(token)
    return await this.userRepository.updateHealthData(user.id, healthData)
  }

  async updateLocation(token: string, location: Point) {
    const user = await this.getUserFromToken(token)
    return await this.userRepository.updateLocation(user.id, location)
  }

  async updateProfilePicture(token: string, url: string | null) {
    const user = await this.getUserFromToken(token)
    return await this.userRepository.updateProfilePicture(user.id, url)
  }

  async getProfile(token: string) {
    return await this.getUserFromToken(token)
  }
}
