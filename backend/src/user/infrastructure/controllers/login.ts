import { InternalServerError, NotFoundError, handleJWTError, validateTokenPresence } from '../../../server/errors/index.js'
import { LoginUser } from '../../application/login.js'
import type { HealthData, Point, UserPreferences } from '../../domain/User.js'

type FirebaseLoginRequest = {
  body: {
    firebaseId: string
    email: string
    name: string
    profilePictureUrl: string
    firebaseToken: string
  }
}

type AuthenticatedRequest = {
  token: string | null
}

type UpdatePreferencesRequest = AuthenticatedRequest & {
  body: UserPreferences
}

type UpdateHealthDataRequest = AuthenticatedRequest & {
  body: HealthData
}

type UpdateLocationRequest = AuthenticatedRequest & {
  body: Point
}

type UpdateProfilePictureRequest = AuthenticatedRequest & {
  body: {
    profilePictureUrl: string | null
  }
}

export class LoginController {
  constructor(private readonly loginUser: LoginUser) {}

  async loginWithFirebase({ body }: FirebaseLoginRequest) {
    try {
      const result = await this.loginUser.loginWithFirebase(body.firebaseId, body.email, body.name, body.profilePictureUrl, body.firebaseToken)
      const userData = result.user.toJSON()

      return {
        status: result.isNewUser ? 201 : 200,
        data: {
          user: {
            id: userData.id,
            name: userData.name,
            email: userData.email,
            profilePictureUrl: userData.profilePictureUrl,
            preferences: userData.preferences,
            healthData: userData.healthData,
            currentLocation: userData.currentLocation
          },
          accessToken: userData.accessToken,
          refreshToken: userData.refreshToken
        }
      }
    } catch (error) {
      // All errors in login are treated as internal server errors
      // unless they're specific validation or authentication errors
      throw new InternalServerError((error as Error).message)
    }
  }

  async refreshToken({ body }: { body: { refreshToken: string } }) {
    try {
      const accessToken = await this.loginUser.refreshAccessToken(body.refreshToken)
      return {
        status: 200,
        data: { accessToken }
      }
    } catch (error) {
      // Handle JWT errors using centralized error handler
      handleJWTError(error)
    }
  }

  async updatePreferences({ body, token }: UpdatePreferencesRequest) {
    validateTokenPresence(token)

    try {
      const user = await this.loginUser.updatePreferences(token, body)
      return {
        status: 200,
        data: user.toJSON()
      }
    } catch (error) {
      const err = error as Error

      // Handle specific business logic errors
      if (err.message === 'User not found') {
        throw new NotFoundError(err.message)
      }

      // Handle JWT errors
      handleJWTError(error)
    }
  }

  async updateHealthData({ body, token }: UpdateHealthDataRequest) {
    validateTokenPresence(token)

    try {
      const user = await this.loginUser.updateHealthData(token, body)
      return {
        status: 200,
        data: user.toJSON()
      }
    } catch (error) {
      const err = error as Error

      // Handle specific business logic errors
      if (err.message === 'User not found') {
        throw new NotFoundError(err.message)
      }

      // Handle JWT errors
      handleJWTError(error)
    }
  }

  async updateLocation({ body, token }: UpdateLocationRequest) {
    validateTokenPresence(token)

    try {
      const user = await this.loginUser.updateLocation(token, body)
      return {
        status: 200,
        data: user.toJSON()
      }
    } catch (error) {
      const err = error as Error

      // Handle specific business logic errors
      if (err.message === 'User not found') {
        throw new NotFoundError(err.message)
      }

      // Handle JWT errors
      handleJWTError(error)
    }
  }

  async updateProfilePicture({ body, token }: UpdateProfilePictureRequest) {
    validateTokenPresence(token)

    try {
      const user = await this.loginUser.updateProfilePicture(token, body.profilePictureUrl)
      return {
        status: 200,
        data: user.toJSON()
      }
    } catch (error) {
      const err = error as Error

      // Handle specific business logic errors
      if (err.message === 'User not found') {
        throw new NotFoundError(err.message)
      }

      // Handle JWT errors
      handleJWTError(error)
    }
  }

  async getProfile({ token }: AuthenticatedRequest) {
    validateTokenPresence(token)

    try {
      const user = await this.loginUser.getProfile(token)
      return {
        status: 200,
        data: user.toJSON()
      }
    } catch (error) {
      const err = error as Error

      // Handle specific business logic errors
      if (err.message === 'User not found') {
        throw new NotFoundError(err.message)
      }

      // Handle JWT errors
      handleJWTError(error)
    }
  }
}
