import { Elysia, t } from 'elysia'
import { loginController } from '../server/dependencies.js'
import { firebaseLoginDTO, refreshTokenDTO } from './domain/userDTO.js'

const updatePreferencesDTO = {
  body: t.Object({
    language: t.String(),
    theme: t.Union([t.Literal('light'), t.Literal('dark'), t.Literal('system')]),
    notifications: t.<PERSON>(),
    defaultLocation: t.Optional(
      t.Object({
        departmentCode: t.String(),
        provinceCode: t.String(),
        districtCode: t.String()
      })
    )
  })
}

const updateHealthDataDTO = {
  body: t.Object({
    allergies: t.Array(t.String()),
    conditions: t.Array(t.String()),
    medications: t.Array(
      t.Object({
        name: t.String(),
        dosage: t.String(),
        frequency: t.String()
      })
    )
  })
}

const updateLocationDTO = {
  body: t.Object({
    latitude: t.Number({ minimum: -90, maximum: 90 }),
    longitude: t.Number({ minimum: -180, maximum: 180 })
  })
}

const updateProfilePictureDTO = {
  body: t.Object({
    profilePictureUrl: t.Union([t.String({ format: 'uri' }), t.Null()])
  })
}

export const userRouter = new Elysia({ prefix: '/users' })
  .derive(({ headers }) => {
    try {
      const auth = headers?.['authorization'] ?? headers?.['Authorization']
      if (auth && typeof auth === 'string' && auth.startsWith('Bearer ')) {
        return { token: auth.slice(7) }
      }
      return { token: null }
    } catch (_error) {
      return { token: null }
    }
  })
  .post(
    '/firebase/login',
    async (context) =>
      await loginController.loginWithFirebase({
        body: context.body
      }),
    firebaseLoginDTO
  )
  .post(
    '/refresh-token',
    async (context) =>
      await loginController.refreshToken({
        body: context.body
      }),
    refreshTokenDTO
  )
  .put(
    '/preferences',
    async (context) =>
      await loginController.updatePreferences({
        body: context.body,
        token: context.token
      }),
    updatePreferencesDTO
  )
  .put(
    '/health-data',
    async (context) =>
      await loginController.updateHealthData({
        body: context.body,
        token: context.token
      }),
    updateHealthDataDTO
  )
  .put(
    '/location',
    async (context) =>
      await loginController.updateLocation({
        body: context.body,
        token: context.token
      }),
    updateLocationDTO
  )
  .put(
    '/profile-picture',
    async (context) =>
      await loginController.updateProfilePicture({
        body: context.body,
        token: context.token
      }),
    updateProfilePictureDTO
  )
  .get(
    '/profile',
    async (context) =>
      await loginController.getProfile({
        token: context.token
      })
  )
