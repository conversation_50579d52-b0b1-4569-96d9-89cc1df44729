import { HealthData, Point, User, UserPreferences } from './User.js'

export interface IUser {
  create(id: string, name: string, email: string, firebaseToken: string, profilePictureUrl: string): Promise<User>
  findById(id: string): Promise<User | null>
  findByEmail(email: string): Promise<User | null>
  updatePreferences(id: string, preferences: Partial<UserPreferences>): Promise<User>
  updateHealthData(id: string, healthData: Partial<HealthData>): Promise<User>
  updateLocation(id: string, location: Point): Promise<User>
  updateProfilePicture(id: string, url: string | null): Promise<User>
  updateFirebaseToken(id: string, token: string): Promise<User>
  updateRefreshToken(id: string, token: string): Promise<User>
  findOrCreateFromFirebase(
    firebaseId: string,
    email: string,
    name: string,
    firebaseToken: string,
    profilePictureUrl: string
  ): Promise<{ user: User; isNewUser: boolean }>
}
